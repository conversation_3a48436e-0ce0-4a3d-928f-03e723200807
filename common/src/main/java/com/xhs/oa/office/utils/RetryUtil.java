package com.xhs.oa.office.utils;

import com.xhs.oa.office.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

/**
 * 重试工具类
 * 用于对不稳定的外部接口调用进行重试操作
 */
@Slf4j
public class RetryUtil {

    /**
     * 执行带重试的操作
     *
     * @param operation    要执行的操作
     * @param maxRetries   最大重试次数
     * @param delayMs      重试间隔时间（毫秒）
     * @param operationName 操作名称，用于日志
     * @param <T>          操作返回值类型
     * @return 操作返回值
     */
    public static <T> T executeWithRetry(Supplier<T> operation, int maxRetries, long delayMs, String operationName) {
        int currentAttempt = 0;

        while (currentAttempt <= maxRetries) {
            try {
                return operation.get();
            } catch (Exception e) {
                currentAttempt++;
                if (currentAttempt <= maxRetries) {
                    log.warn("{} - 第{}次请求失败，准备重试: {}", operationName, currentAttempt, e.getMessage());
                    try {
                        Thread.sleep(delayMs);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                } else {
                    log.error("{} - 重试{}次后失败: {}", operationName, maxRetries, e.getMessage(), e);
                    throw new BusinessException(operationName + "请求失败: " + e.getMessage(), e);
                }
            }
        }
        throw new BusinessException(operationName + "请求重试异常");
    }
}
