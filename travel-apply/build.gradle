plugins {
    id 'java'
}

group = 'com.xhs.oa.office'
version = '0.0.1-SNAPSHOT'

repositories {
    mavenCentral()
}

dependencies {

    implementation project(':common')

    //spring
    compileOnly("org.mapstruct:mapstruct:1.5.5.Final")
    annotationProcessor("org.mapstruct:mapstruct-processor:1.5.5.Final")
    implementation("jakarta.persistence:jakarta.persistence-api:2.2.3")
    implementation(enforcedPlatform("com.xiaohongshu:infra-root-pom:${root_pom_version}"))
    implementation('com.xiaohongshu:thrift-springboot') { exclude group: 'com.alibaba', module: 'fastjson' }
    implementation('io.swagger:swagger-models:1.5.21')
    // 公司内基础组件
    implementation('com.xiaohongshu.infra.midware:redschedule-spring-boot-starter:1.1.2')
    //fastjson2
    implementation("com.alibaba.fastjson2:fastjson2:2.0.47")

    // db
    implementation('mysql:mysql-connector-java:8.0.16')
    implementation('org.mybatis:mybatis:3.5.5')
    implementation('org.mybatis:mybatis-spring:2.0.5')
    implementation('com.baomidou:mybatis-plus-boot-starter:3.4.0')
    implementation('com.baomidou:mybatis-plus-extension:3.4.0')
    implementation('com.github.pagehelper:pagehelper-spring-boot-starter:1.2.3')

    //企业中台
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-oamiddle:master-SNAPSHOT')
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-crossroad-idl:master-SNAPSHOT')
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-ehr-public:1.0.23')

    implementation('com.xiaohongshu.fls.thrift:lib-thrift-oa-common:sit-SNAPSHOT') //oacommon
    implementation('com.xiaohongshu.fls.thrift:lib-thrift-ehrservice:1.5.9') // 人事

    implementation('com.xhs.enterprise:erp-common:1.1.4-SNAPSHOT') {
        changing = true
        exclude group: 'com.alibaba', module: 'fastjson'
        exclude group: 'com.ctrip.framework.apollo', module: 'apollo-openapi'
        exclude group: 'com.ctrip.framework.apollo', module: 'apollo-core'
        exclude group: 'com.xuxueli', module: 'xxl-job-core'
        exclude group: 'javax.servlet', module: 'javax.servlet-api'
    }
    // 携程
    implementation('corp.openapicalls.lib:webapi:6.9.4@jar')
}

test {
    useJUnitPlatform()
}