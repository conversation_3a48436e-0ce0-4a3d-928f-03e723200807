package com.xhs.oa.office.boot;

import com.xhs.ehr.rpc.service.EhrDepartmentService;
import com.xhs.ehr.rpc.service.EhrEmployeeRecordService;
import com.xhs.ehr.rpc.service.EhrEmployeeService;
import com.xhs.ehr_public.rpc.service.EhrEmployeePrivateInfoService;
import com.xhs.rbac.rpc.service.RbacRpcCoreService;
import com.xhs.rbacx.rpc.service.RbacxRpcCoreService;
import com.xiaohongshu.finance.rpc.rftreasury.CnapsCodeQueryServiceRpc;
import com.xiaohongshu.fls.finance.rpc.workflow.process.operator.OaFlowRuntimeProcessRpc;
import com.xiaohongshu.fls.finance.rpc.workflow.task.operator.OaFlowRuntimeTaskRpc;
import com.xiaohongshu.fls.rpc.enterprise.apihub.common.CommonConstants;
import com.xiaohongshu.fls.rpc.enterprise.apihub.service.ApiCallService;
import com.xiaohongshu.fls.rpc.finance.cashiercenter.PaymentService;
import com.xiaohongshu.fls.rpc.finance.employee.EmployeeService;
import com.xiaohongshu.fls.rpc.himalaya.HimalayaService;
import com.xiaohongshu.fls.rpc.oacommon.company.CompanyService;
import com.xiaohongshu.fls.rpc.oacontract.baiwang.service.ExternalInvoiceService;
import com.xiaohongshu.fls.rpc.oamiddle.msg.service.MsgMiddleRpcService;
import com.xiaohongshu.infra.rpc.client.ClientBuilder;
import com.xiaohongshu.infra.rpc.core.ThriftServiceClientProxyFactory;
import com.xiaohongshu.infra.rpc.core.registry.eds.ThriftServerAddressEdsManager;
import com.xiaohongshu.infra.rpc.springboot.config.RpcConsulConfig;
import com.xiaohongshu.infra.rpc.springboot.config.ZipkinConfig;
import com.xiaohongshu.infrastructure.sms.api.TSmsService;
import com.xiaohongshu.sns.rpc.multicdn.Executor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Lazy;

import java.time.Duration;

@Configuration
@Import({RpcConsulConfig.class, ZipkinConfig.class})
public class RpcConfiguration {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ThriftServerAddressEdsManager thriftServerAddressEdsManager;


    @Bean
    public RbacRpcCoreService.Iface getRbacRpcCoreService() throws Exception {
        ThriftServiceClientProxyFactory thriftServiceClientProxyFactory = new ThriftServiceClientProxyFactory();
        thriftServiceClientProxyFactory.setService("com.xhs.rbac.rpc.service.RbacRpcCoreService");
        thriftServiceClientProxyFactory.setServerAddressProvider(thriftServerAddressEdsManager);
        thriftServiceClientProxyFactory.setApplicationContext(applicationContext);
        thriftServiceClientProxyFactory.setSocketTimeout(5000);
        thriftServiceClientProxyFactory.afterPropertiesSet();
        return (RbacRpcCoreService.Iface) thriftServiceClientProxyFactory.getObject();
    }

    @Bean
    public RbacxRpcCoreService.Iface getRbacxRpcCoreService() throws Exception {
        ThriftServiceClientProxyFactory thriftServiceClientProxyFactory = new ThriftServiceClientProxyFactory();
        thriftServiceClientProxyFactory.setService("com.xhs.rbacx.rpc.service.RbacxRpcCoreService");
        thriftServiceClientProxyFactory.setServerAddressProvider(thriftServerAddressEdsManager);
        thriftServiceClientProxyFactory.setApplicationContext(applicationContext);
        thriftServiceClientProxyFactory.setSocketTimeout(5000);
        thriftServiceClientProxyFactory.afterPropertiesSet();
        return (RbacxRpcCoreService.Iface) thriftServiceClientProxyFactory.getObject();
    }

    @Bean
    public EmployeeService.Iface employeeService() throws Exception {
        ThriftServiceClientProxyFactory thriftServiceClientProxyFactory = new ThriftServiceClientProxyFactory();
        thriftServiceClientProxyFactory.setSocketTimeout(5000);
        thriftServiceClientProxyFactory.setService("com.xiaohongshu.fls.rpc.finance.employee.EmployeeService");
        thriftServiceClientProxyFactory.setServerAddressProvider(thriftServerAddressEdsManager);
        thriftServiceClientProxyFactory.setApplicationContext(applicationContext);
        thriftServiceClientProxyFactory.afterPropertiesSet();
        return (EmployeeService.Iface) thriftServiceClientProxyFactory.getObject();
    }

    @Bean()
    public TSmsService.Iface provideSmsService() throws Exception {
        ThriftServiceClientProxyFactory thriftServiceClientProxyFactory = new ThriftServiceClientProxyFactory();
        thriftServiceClientProxyFactory.setSocketTimeout(3000);
        thriftServiceClientProxyFactory.setThriftClass(TSmsService.class);
        thriftServiceClientProxyFactory.setServerAddressProvider(thriftServerAddressEdsManager);
        thriftServiceClientProxyFactory.setApplicationContext(applicationContext);
        thriftServiceClientProxyFactory.setCallLog(true);
        thriftServiceClientProxyFactory.afterPropertiesSet();
        return (TSmsService.Iface) thriftServiceClientProxyFactory.getObject();
    }

    @Lazy
    @Bean
    public ExternalInvoiceService.Iface invoiceValidateService() throws Exception {
        com.xiaohongshu.infra.rpc.core.ThriftServiceClientProxyFactory thriftServiceClientProxyFactory = new com.xiaohongshu.infra.rpc.core.ThriftServiceClientProxyFactory();
        thriftServiceClientProxyFactory.setSocketTimeout(100000);
        thriftServiceClientProxyFactory.setService("com.xiaohongshu.fls.rpc.oacontract.baiwang.service.ExternalInvoiceService");
        thriftServiceClientProxyFactory.setServerAddressProvider(thriftServerAddressEdsManager);
        thriftServiceClientProxyFactory.setApplicationContext(applicationContext);
        thriftServiceClientProxyFactory.afterPropertiesSet();
        return (ExternalInvoiceService.Iface) thriftServiceClientProxyFactory.getObject();
    }

    @Bean
    public Executor.Iface getExecutor() throws Exception {
        ThriftServiceClientProxyFactory thriftServiceClientProxyFactory = new ThriftServiceClientProxyFactory();
        thriftServiceClientProxyFactory.setServiceName("multicdn-service-executor");
        thriftServiceClientProxyFactory.setService(Executor.class.getName());
        thriftServiceClientProxyFactory.setServerAddressProvider(thriftServerAddressEdsManager);
        thriftServiceClientProxyFactory.setApplicationContext(applicationContext);
        thriftServiceClientProxyFactory.setSocketTimeout(10000);
        thriftServiceClientProxyFactory.afterPropertiesSet();
        return (Executor.Iface) thriftServiceClientProxyFactory.getObject();
    }

    @Bean
    public HimalayaService.Iface himalayaService() throws Exception {
        ThriftServiceClientProxyFactory thriftServiceClientProxyFactory = new ThriftServiceClientProxyFactory();
        thriftServiceClientProxyFactory.setSocketTimeout(5000);
        thriftServiceClientProxyFactory.setService("com.xiaohongshu.fls.rpc.himalaya.HimalayaService");
        thriftServiceClientProxyFactory.setServerAddressProvider(thriftServerAddressEdsManager);
        thriftServiceClientProxyFactory.setApplicationContext(applicationContext);
        thriftServiceClientProxyFactory.afterPropertiesSet();
        return (HimalayaService.Iface) thriftServiceClientProxyFactory.getObject();
    }

    @Bean
    public EhrEmployeeService.Iface ehrEmployeeRpcClient() {
        return ClientBuilder.create(EhrEmployeeService.Iface.class, "com.xhs.ehr.rpc.service.EhrEmployeeService")
                .withTimeout(Duration.ofSeconds(10L))
                .buildStub();
    }

    @Bean
    public EhrDepartmentService.Iface ehrDepartmentRpcClient() {
        return ClientBuilder.create(EhrDepartmentService.Iface.class, "com.xhs.ehr.rpc.service.EhrDepartmentService")
                .withTimeout(Duration.ofSeconds(10L))
                .buildStub();
    }

    @Bean
    public OaFlowRuntimeProcessRpc.Iface oaFlowRuntimeProcessRpcClient() {
        return ClientBuilder.create(OaFlowRuntimeProcessRpc.Iface.class, "com.xiaohongshu.fls.finance.rpc.workflow.process.operator.OaFlowRuntimeProcessRpc")
                .withTimeout(Duration.ofSeconds(10L))
                .buildStub();
    }

    @Bean
    public OaFlowRuntimeTaskRpc.Iface oaFlowRuntimeTaskRpcClient() {
        return ClientBuilder.create(OaFlowRuntimeTaskRpc.Iface.class, "com.xiaohongshu.fls.finance.rpc.workflow.task.operator.OaFlowRuntimeTaskRpc")
                .withTimeout(Duration.ofSeconds(10L))
                .buildStub();
    }

    @Bean
    public HimalayaService.Iface financeHimalayaServiceRpcClient() {
        return ClientBuilder.create(HimalayaService.Iface.class, "com.xiaohongshu.fls.rpc.himalaya.HimalayaService")
                .withTimeout(Duration.ofSeconds(10L))
                .buildStub();
    }

    @Bean
    public PaymentService.Iface financePaymentServiceRpcClient() {
        return ClientBuilder.create(PaymentService.Iface.class, "com.xiaohongshu.fls.rpc.finance.cashiercenter.PaymentService")
                .withTimeout(Duration.ofSeconds(10L))
                .buildStub();
    }

    @Bean
    public CompanyService.Iface oaCompanyServiceRpcClient() {
        return ClientBuilder.create(CompanyService.Iface.class, "com.xiaohongshu.fls.rpc.oacommon.company.CompanyService")
                .withTimeout(Duration.ofSeconds(10L))
                .buildStub();
    }

    @Bean
    public ApiCallService.Iface apiCallServiceRpcClient() {
        return ClientBuilder.create(ApiCallService.Iface.class, CommonConstants.SERVICE_NAME)
                .withTimeout(Duration.ofSeconds(10L))
                .buildStub();
    }

    @Bean
    public MsgMiddleRpcService.Iface msgMiddleRpcService() {
        return ClientBuilder.create(MsgMiddleRpcService.Iface.class, "com.xiaohongshu.fls.rpc.oamiddle.msg.service.MsgMiddleRpcService")
                .withTimeout(Duration.ofSeconds(10L))
                .buildStub();
    }

    @Bean
    public EhrEmployeeRecordService.Iface ehrEmployeeRecordRpcClient() {
        return ClientBuilder.create(EhrEmployeeRecordService.Iface.class, "com.xhs.ehr.rpc.service.ehremployeerecordservice")
                .withTimeout(Duration.ofSeconds(3L))
                .buildStub();
    }

    @Bean
    public EhrEmployeePrivateInfoService.Iface ehrEmployeePublicInfoServiceRpc() {
        return ClientBuilder.create(EhrEmployeePrivateInfoService.Iface.class, "com.xhs.ehr_public.rpc.service.EhrEmployeePrivateInfoService")
                .withTimeout(Duration.ofSeconds(3L))
                .buildStub();
    }

    @Bean
    public CnapsCodeQueryServiceRpc.Iface cnapsCodeQueryServiceRpc() {
        return ClientBuilder.create(CnapsCodeQueryServiceRpc.Iface.class, "rftreasury-service-default")
                .withTimeout(Duration.ofSeconds(5L))
                .buildStub();
    }
}

