package com.xhs.oa.form.service;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import com.xhs.rbac.client.common.RbacApiConstant;
import com.xhs.rbac.client.dto.meta.RbacApiCallMetaInfo;
import com.xhs.rbac.client.service.v2.impl.RbacClientCoreService;
import com.xiaohongshu.erp.common.monitor.CatTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-07-06 17:13
 * @Version 1.0
 **/
@Slf4j
@Service
public class RbacxClientService {

    final static public RbacApiCallMetaInfo META_INFO;

    static {
        META_INFO = new RbacApiCallMetaInfo();
        META_INFO.setAppName("xhsoa");
        META_INFO.setAppToken("xhsoa");
        META_INFO.setApiVersion(RbacApiConstant.API_VERSION_2);
    }

    @ApolloJsonValue("${rbacx_app_code_list:[]}")
    public List<String> rbacxAppList;

    @Autowired
    private RbacClientCoreService rbacClientCoreService;

    @CatTransaction
    public List<String> getPermissionCodesByEmailAndAppNames(String email) {

        List<String> permissionCodesResult = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(rbacxAppList)){
            for (String app : rbacxAppList) {
                // 获取单个系统权限
                List<String> permissionCodeList = getPermissionCodesByEmailAndAppName(email, app, app);
                if (CollectionUtils.isNotEmpty(permissionCodeList)) {
                    // 拼装所有权限
                    permissionCodesResult.addAll(permissionCodeList);
                }
            }
        }
        return permissionCodesResult;
    }

    @CatTransaction
    public List<String> getPermissionCodesByEmailAndAppName(String email, String appName, String appToken) {
        List<String> permissionCodeList = null;
        try {
            permissionCodeList = rbacClientCoreService.listAllPermissionCodeByEmail(getRbacApiCallMetaInfo(appName, appToken), email);
        } catch (Exception e) {
            log.error("RbacxClientService listAllPermissionCodeByEmail email:{},appName:{},error:{}", email, appName, e);
        }
        return permissionCodeList;
    }

    public RbacApiCallMetaInfo getRbacApiCallMetaInfo(String appName, String appToken) {
        RbacApiCallMetaInfo meta = new RbacApiCallMetaInfo();
        meta.setAppName(appName);
        meta.setAppToken(appToken);
        meta.setApiVersion(RbacApiConstant.API_VERSION_2);
        meta.setNamespaceList(Lists.newArrayList());
        return meta;
    }
}
