package com.xhs.reimburse.constant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class CommonConstant {

    public static final Integer OCR_INSPECTION_STATUS_SUCCESS = 1;

    public static final String FORM_COMPONENT_SUFFIX = "FormFlowComponentService";

    // 费用超标说明-水位
    public static final String EXPENSE_CUSTOMER_INFO_WATER_LEVEL = "waterLevel";
    // 费用超标说明-费用金额不等于发票金额时
    public static final String EXPENSE_CUSTOMER_INFO_RELATION_INVOICE_AMOUNT = "relationInvoiceAmount";

    // 关联发票
    public static final String EXPENSE_FIELD_ID_CODE_RELATION_INVOICE = "relationInvoice";
    // 附件
    public static final String EXPENSE_FIELD_ID_CODE_UPLOADER = "attachment";
    // 公司成员
    public static final String EXPENSE_FIELD_ID_CODE_COMPANY_MEMBERS = "companyMembers";
    // 外部成员
    public static final String EXPENSE_FIELD_ID_CODE_OUTSIDE_ROOMMATE = "outsideRoommate";
    // 消费日期
    public static final String EXPENSE_FIELD_ID_CODE_DATE_PHASE = "datePhase";
    // 报销金额
    public static final String EXPENSE_FIELD_ID_CODE_AMOUNT = "amount";
    // 费用说明
    public static final String EXPENSE_FIELD_ID_CODE_COST_COMMENT = "costComment";
    // 餐饮-用餐城市
    public static final String EXPENSE_FIELD_ID_CODE_CITY = "city";
    // 餐饮-人数
    public static final String EXPENSE_FIELD_ID_CODE_NUM_OF_PEOPLE = "numberOfPeople";
    // 餐饮-人均费用
    public static final String EXPENSE_FIELD_ID_CODE_PER_CAPITA_COST = "perCapitaCost";
    // 飞机票/火车票 出发-到达城市&座位类型
    public static final String EXPENSE_FIELD_ID_CODE_TRIP_ROUTE_CITY = "tripRouteCity";
    // 差旅城市code
    public static final String EXPENSE_FIELD_ID_CODE_CITY_CODE = "cityCode";
    // 差旅-境内工作餐 多城市code
    public static final String EXPENSE_FIELD_ID_CODE_TRIP_CITY_MULTIPLE_CODE = "tripCityMultiple";
    // 差旅-出租车城市-多选
    public static final String EXPENSE_FIELD_ID_CODE_CITY_MULTIPLE = "cityMultiple";

    // 出差天数
    public static final String EXPENSE_FIELD_ID_CODE_STAY_DAYS = "stayDays";
    public static final String EXPENSE_FIELD_ID_CODE_TRIP_CITY = "tripCity";
    // 可报销金额
    public static final String EXPENSE_FIELD_ID_CODE_REIMBURSABLE_AMOUNT = "reimbursableAmount";

    /**
     * 发票字段枚举
     */
    //发票号码
    public static final String INVOICE_FIELD_ID_CODE_INVOICE_NO = "invoiceNo";
    // 发票代码
    public static final String INVOICE_FIELD_ID_CODE_INVOICE_CODE = "invoiceCode";
    // 开票日期
    public static final String INVOICE_FIELD_ID_CODE_INVOICE_DATE = "invoiceDate";
    // 税额
    public static final String INVOICE_FIELD_ID_CODE_TOTAL_TAX = "totalTax";
    // 总金额
    public static final String INVOICE_FIELD_ID_CODE_TOTAL_AMOUNT = "totalAmount";
    // 购方名称
    public static final String INVOICE_FIELD_ID_CODE_PURCHASER_NAME = "purchaserName";
    // 购方税号
    public static final String INVOICE_FIELD_ID_CODE_PURCHASER_TAX_NO = "purchaserTaxNo";
    // 销方名称
    public static final String INVOICE_FIELD_ID_CODE_SALE_NAME = "saleName";
    // 销方税号
    public static final String INVOICE_FIELD_ID_CODE_SALE_TAX_NO = "saleTaxNo";

    public static final String INVOICE_FIELD_ID_CODE_LEAVE_TIME = "leaveTime";
    public static final String INVOICE_FIELD_ID_CODE_ARRIVE_TIME = "arriveTime";
    public static final String INVOICE_FIELD_ID_CODE_LEAVE_PLACE = "leavePlace";
    public static final String INVOICE_FIELD_ID_CODE_ARRIVE_PLACE = "arrivePlace";
    public static final String INVOICE_FIELD_ID_CODE_SEAT_TYPE = "trainSeat";

    public static final String INVOICE_FIELD_ID_CODE_MILEAGE = "mileage";

    public static final String INVOICE_FIELD_ID_CODE_TIME = "time";
    public static final String INVOICE_FIELD_ID_CODE_EXIT = "exit";
    public static final String INVOICE_FIELD_ID_CODE_ENTRANCE = "entrance";

    public static final String INVOICE_FIELD_ID_CODE_DRAWER = "drawer";
    public static final String INVOICE_FIELD_ID_CODE_TRAIN_SEAT = "trainSeat";
    public static final String INVOICE_FIELD_ID_CODE_TRAIN_NUMBER = "trainNumber";

    public static final String INVOICE_FIELD_ID_CODE_FROM_STATION = "fromStation";
    public static final String INVOICE_FIELD_ID_CODE_TO_STATION = "toStation";
    public static final String INVOICE_FIELD_ID_CODE_SEAT_LEVEL = "seatLevel";
    public static final String INVOICE_FIELD_ID_CODE_FLIGHT_NO = "flightNo";
    public static final String INVOICE_FIELD_ID_CODE_ID_NUM = "idNum";


    public static final String INVOICE_FIELD_ID_CODE_STARTCITY = "leavePlace";
    public static final String INVOICE_FIELD_ID_CODE_ENDCITY = "arrivePlace";
    public static final String INVOICE_FIELD_ID_CODE_SEATTYPE = "trainSeat";

    //常用常量
    public static final Byte VALID = 1;
    public static final Integer VALID_INTER = 1;
    public static final Byte INVALID = 0;
    public static final BigDecimal NEED_AUDIT_AMOUNT = new BigDecimal("2000");

    public static final String DYNAMIC_FORM_COMPONENT_MONEY = "money";
}
