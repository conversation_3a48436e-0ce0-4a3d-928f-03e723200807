package com.xhs.reimburse.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xhs.reimburse.service.PaymentRedFlowService;
import com.xhs.reimburse.xhsoa.mapper.CommonFormMapper;
import com.xhs.reimburse.xhsoa.mapper.FormPaymentDetailMapper;
import com.xhs.reimburse.xhsoa.modal.CommonForm;
import com.xhs.reimburse.xhsoa.modal.FormPaymentDetail;
import com.xiaohongshu.events.client.MessageExt;
import com.xiaohongshu.events.client.api.MessageProcessor;
import com.xiaohongshu.events.client.consumer.ConsumeContext;
import com.xiaohongshu.events.client.consumer.ConsumeStatus;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

import static com.xhs.reimburse.service.external.finance.CashierRpcService.SOURCE_TYPE;

/**
 * 付款消息消费逻辑
 */
@Component
@Slf4j
public class PaymentMessageProcessor implements MessageProcessor {
    // 支付状态常量
    public static final String PAY_SUCCESS = "PAY_SUCCESS";
    public static final String PAY_FAIL = "PAY_FAIL";
    public static final String PAY_RETURN = "PAY_RETURN";
    public static final String APPROVE_REJECT = "APPROVE_REJECT";
    public static final String FORM_PAY_SUCCESS = "payment_success";
    public static final String FORM_PAY_FAIL = "payment_failure";
    @Resource
    private FormPaymentDetailMapper formPaymentDetailMapper;
    @Resource
    private CommonFormMapper commonFormMapper;
    @Resource
    private PaymentRedFlowService paymentRedFlowService;

    @Override
    public ConsumeStatus process(MessageExt msg, ConsumeContext context) {
        String msgBody = new String(msg.getBody());
        log.info("FinanceMessageProcessor - 收到财务消息: {}", msgBody);

        try {
            // 1. 消息解析和验证
            PaymentNotifyDto paymentNotify = parseAndValidateMessage(msgBody);
            if (paymentNotify == null) {
                return ConsumeStatus.SUCCESS;
            }
            // 2. 只处理本系统的消息
            if (!SOURCE_TYPE.equals(paymentNotify.getSourceType())) {
                log.info("FinanceMessageProcessor - 非本系统消息，跳过处理，sourceType: {}", paymentNotify.getSourceType());
                return ConsumeStatus.SUCCESS;
            }
            // 3. 根据支付状态进行业务处理
            processPaymentStatus(paymentNotify);
        } catch (Exception e) {
            log.error("FinanceMessageProcessor - 处理财务消息异常: {}", e.getMessage(), e);
            return ConsumeStatus.SUCCESS;
        }
        return ConsumeStatus.SUCCESS;
    }

    /**
     * 解析和验证消息
     */
    private PaymentNotifyDto parseAndValidateMessage(String msgBody) {
        if (StringUtils.isBlank(msgBody)) {
            log.warn("FinanceMessageProcessor - 消息体为空");
            return null;
        }
        try {
            PaymentNotifyDto paymentNotify = JSON.parseObject(msgBody, PaymentNotifyDto.class);
            if (paymentNotify == null) {
                log.warn("FinanceMessageProcessor - 消息解析失败");
                return null;
            }
            // 验证必要字段
            if (StringUtils.isBlank(paymentNotify.getSourceNo()) || StringUtils.isBlank(paymentNotify.getStatus())) {
                log.warn("FinanceMessageProcessor - 消息缺少必要字段，sourceNo: {}, status: {}", paymentNotify.getSourceNo(), paymentNotify.getStatus());
                return null;
            }
            return paymentNotify;
        } catch (Exception e) {
            log.error("FinanceMessageProcessor - 消息解析异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据支付状态进行业务处理
     */
    private void processPaymentStatus(PaymentNotifyDto paymentNotify) {
        String status = paymentNotify.getStatus();
        String sourceNo = paymentNotify.getSourceNo();
        log.info("FinanceMessageProcessor - 处理支付状态，sourceNo: {}, status: {}", sourceNo, status);
        switch (status) {
            case PAY_SUCCESS:
                // 支付成功：业务单据仍然为审核中，不做处理
                handlePaymentSuccess(paymentNotify);
                break;
            case PAY_FAIL:
                // 支付失败：业务单驳回到发起人，驳回原因为付款失败原因
                handlePaymentFail(paymentNotify);
                break;
            case PAY_RETURN:
                // 支付退票：业务单据驳回到发起人，驳回原因为退票原因
                handlePaymentReturn(paymentNotify);
                break;
            case APPROVE_REJECT:
                // 审批拒绝：业务单驳回到发起人，驳回原因为审批拒绝原因
                handleApproveReject(paymentNotify);
                break;
            default:
                log.info("FinanceMessageProcessor - 未处理的支付状态: {}", status);
                break;
        }
    }

    /**
     * 处理支付成功
     */
    private void handlePaymentSuccess(PaymentNotifyDto paymentNotify) {
        log.info("FinanceMessageProcessor - 处理支付成功，sourceNo: {}", paymentNotify.getSourceNo());
        // 事务性处理数据库更新
        processPaymentSuccessWithTransaction(paymentNotify);
        log.info("FinanceMessageProcessor - 支付成功处理完成，sourceNo: {}", paymentNotify.getSourceNo());
    }

    /**
     * 事务性处理支付成功
     */
    public void processPaymentSuccessWithTransaction(PaymentNotifyDto paymentNotify) {
        try {
            // 更新付款详情表状态
            updatePaymentDetailStatus(paymentNotify, PAY_SUCCESS, null);
            // 更新通用表单支付状态
            updateCommonFormPayStatus(paymentNotify.getSourceNo(), FORM_PAY_SUCCESS);
        } catch (Exception e) {
            log.error("FinanceMessageProcessor - 事务性处理支付成功异常，sourceNo: {}, error: {}", paymentNotify.getSourceNo(), e.getMessage(), e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 处理支付失败
     */
    private void handlePaymentFail(PaymentNotifyDto paymentNotify) {
        String failReason = buildFailReason("付款失败", paymentNotify);
        log.info("FinanceMessageProcessor - 处理支付失败，sourceNo: {}, reason: {}", paymentNotify.getSourceNo(), failReason);
        // 事务性处理数据库更新和流程驳回
        processPaymentFailureWithTransaction(paymentNotify, failReason);
        log.info("FinanceMessageProcessor - 支付失败处理完成，sourceNo: {}", paymentNotify.getSourceNo());
    }

    /**
     * 处理支付退票
     */
    private void handlePaymentReturn(PaymentNotifyDto paymentNotify) {
        String returnReason = buildFailReason("付款退票", paymentNotify);
        log.info("FinanceMessageProcessor - 处理支付退票，sourceNo: {}, reason: {}", paymentNotify.getSourceNo(), returnReason);
        // 事务性处理数据库更新和流程驳回
        processPaymentFailureWithTransaction(paymentNotify, PAY_RETURN, returnReason);
        log.info("FinanceMessageProcessor - 支付退票处理完成，sourceNo: {}", paymentNotify.getSourceNo());
    }

    /**
     * 处理审批拒绝
     */
    private void handleApproveReject(PaymentNotifyDto paymentNotify) {
        String rejectReason = buildFailReason("审批拒绝", paymentNotify);
        log.info("FinanceMessageProcessor - 处理审批拒绝，sourceNo: {}, reason: {}",paymentNotify.getSourceNo(), rejectReason);
        // 事务性处理数据库更新和流程驳回
        processPaymentFailureWithTransaction(paymentNotify, APPROVE_REJECT, rejectReason);
        log.info("FinanceMessageProcessor - 审批拒绝处理完成，sourceNo: {}", paymentNotify.getSourceNo());
    }

    /**
     * 事务性处理支付失败情况（包括失败、退票、审批拒绝）
     */
    public void processPaymentFailureWithTransaction(PaymentNotifyDto paymentNotify, String failReason) {
        processPaymentFailureWithTransaction(paymentNotify, PAY_FAIL, failReason);
    }

    /**
     * 事务性处理支付失败情况（包括失败、退票、审批拒绝）
     */
    public void processPaymentFailureWithTransaction(PaymentNotifyDto paymentNotify, String status, String reason) {
        try {
            // 更新付款详情表状态
            updatePaymentDetailStatus(paymentNotify, status, reason);
            // 更新通用表单支付状态
            updateCommonFormPayStatus(paymentNotify.getSourceNo(), FORM_PAY_FAIL);
            // 驳回流程到发起人
            rejectToInitiator(paymentNotify.getSourceNo(), reason);
        } catch (Exception e) {
            log.error("FinanceMessageProcessor - 事务性处理支付失败异常，sourceNo: {}, error: {}", paymentNotify.getSourceNo(), e.getMessage(), e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 构建失败原因
     */
    private String buildFailReason(String prefix, PaymentNotifyDto paymentNotify) {
        StringBuilder reason = new StringBuilder(prefix);
        if (StringUtils.isNotBlank(paymentNotify.getErrorMsg())) {
            reason.append("：").append(paymentNotify.getErrorMsg());
        } else if (paymentNotify.getErrorCode() != 0) {
            reason.append("：错误码").append(paymentNotify.getErrorCode());
            if (StringUtils.isNotBlank(paymentNotify.getSubErrorCode())) {
                reason.append("，子错误码").append(paymentNotify.getSubErrorCode());
            }
        }
        return reason.toString();
    }

    /**
     * 更新付款详情表状态
     */
    private void updatePaymentDetailStatus(PaymentNotifyDto paymentNotify, String status, String reason) {
        try {
            String sourceNo = paymentNotify.getSourceNo();
            // 根据sourceNo查询付款详情
            QueryWrapper<FormPaymentDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("payment_no", sourceNo).eq("is_valid", 1);
            FormPaymentDetail paymentDetail = formPaymentDetailMapper.selectOne(queryWrapper);
            if (paymentDetail == null) {
                log.warn("FinanceMessageProcessor - 未找到付款详情记录，sourceNo: {}", sourceNo);
                return;
            }
            // 更新状态
            UpdateWrapper<FormPaymentDetail> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("payment_no", sourceNo).eq("is_valid", 1);
            updateWrapper.set("payment_status", status);
            updateWrapper.set("update_time", new Date());
            if (StringUtils.isNotBlank(reason)) {
                updateWrapper.set("reason", reason);
            }
            if (PAY_SUCCESS.equals(status) && paymentNotify.getOccurrence() != null) {
                updateWrapper.set("payment_success_time", new Date(paymentNotify.getOccurrence()));
            }
            int updateCount = formPaymentDetailMapper.update(null, updateWrapper);
            log.info("FinanceMessageProcessor - 更新付款详情表，sourceNo: {}, status: {}, updateCount: {}", sourceNo, status, updateCount);
        } catch (Exception e) {
            log.error("FinanceMessageProcessor - 更新付款详情表异常，sourceNo: {}, error: {}", paymentNotify.getSourceNo(), e.getMessage(), e);
        }
    }

    /**
     * 更新通用表单支付状态
     */
    private void updateCommonFormPayStatus(String sourceNo, String payStatus) {
        try {
            // 从sourceNo中提取formNum（去掉前缀后的部分）
            String formNum = getFormNum(sourceNo);
            if (StringUtils.isBlank(formNum)) {
                log.warn("FinanceMessageProcessor - 无法从sourceNo提取formNum: {}", sourceNo);
                return;
            }
            UpdateWrapper<CommonForm> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("form_num", formNum).eq("is_valid", 1);
            updateWrapper.set("pay_status", payStatus);
            updateWrapper.set("update_time", new Date());
            int updateCount = commonFormMapper.update(null, updateWrapper);
            log.info("FinanceMessageProcessor - 更新通用表单支付状态，formNum: {}, payStatus: {}, updateCount: {}", formNum, payStatus, updateCount);
        } catch (Exception e) {
            log.error("FinanceMessageProcessor - 更新通用表单支付状态异常，sourceNo: {}, error: {}", sourceNo, e.getMessage(), e);
        }
    }

    /**
     * 驳回流程到发起人
     */
    private void rejectToInitiator(String sourceNo, String reason) {
        try {
            // 从sourceNo中提取formNum
            String formNum = getFormNum(sourceNo);
            if (StringUtils.isBlank(formNum)) {
                log.warn("FinanceMessageProcessor - 无法从sourceNo提取formNum进行驳回: {}", sourceNo);
                return;
            }
            paymentRedFlowService.flowRefuse(formNum, reason);
        } catch (Exception e) {
            log.error("FinanceMessageProcessor - 驳回流程异常，sourceNo: {}, error: {}", sourceNo, e.getMessage(), e);
        }
    }

    private String getFormNum(String sourceNo) {
        if (StringUtils.isBlank(sourceNo)) {
            return null;
        }
        // 通过sourceNo查询form_payment_detail表，获取form_no
        FormPaymentDetail paymentDetail = formPaymentDetailMapper.selectByPaymentNo(sourceNo);
        if (paymentDetail == null) {
            return null;
        }
        return paymentDetail.getFormNo();
    }

    /**
     * 财务付款通知消息DTO
     */
    @Data
    public static class PaymentNotifyDto {
        // 来源类型
        private String sourceType;
        // 来源业务类型
        private String sourceBizType;
        // 来源编号
        private String sourceNo;
        // 小红书编号
        private String xhsNo;
        // 状态
        private String status;
        // 业务状态实际发生时间
        private Long occurrence;
        // 错误编码
        private int errorCode;
        // 子错误编码
        private String subErrorCode;
        // 错误信息
        private String errorMsg;
    }
}