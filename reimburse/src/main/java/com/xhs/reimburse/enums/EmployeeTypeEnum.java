package com.xhs.reimburse.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date :2025/07/25 - 下午2:43
 * @description :
 */
@AllArgsConstructor
@Getter
public enum EmployeeTypeEnum {
    FORMAL_EMP(1,"正式员工"),
    INTERNSHIP_EMP(2,"实习员工"),
    SIDE_EMP(3,"编外人员"),
    DISPATCHED_EMP(4, "派遣员工"),
    OUTER_BPO(5, "BPO");

    private int code;
    private String name;
}
