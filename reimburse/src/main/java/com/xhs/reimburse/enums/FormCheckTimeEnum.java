package com.xhs.reimburse.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date :2025/08/01 - 上午10:27
 * @description :
 */
@Getter
@AllArgsConstructor
public enum FormCheckTimeEnum {
    BEFORE_SUBMISSION("BEFORE_SUBMISSION", "单据提交前"),
    AFTER_SUBMISSION("AFTER_SUBMISSION", "单据提交后");

    @EnumValue
    private final String time;
    private final String desc;
}