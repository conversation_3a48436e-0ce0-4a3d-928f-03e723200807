package com.xhs.reimburse.enums;

import com.xhs.reimburse.modal.dto.CheckResultDetailDto;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date :2025/07/08 - 下午5:25
 * @description :报销校验项目
 */
@Getter
@AllArgsConstructor
public enum ReimbursementFormCheckItemEnum {
    /**
     * 用户身份校验
     */
    FORM_AUTH_CHECK("FORM_AUTH_CHECK", "用户身份校验", true, false, "", ""),

    /**
     * 单据状态校验
     */
    FORM_STATUS_CHECK("FORM_STATUS_CHECK", "单据状态校验", true, false, "", ""),

    /**
     * 单据基础信息校验
     */
    FORM_BASE_INFO_CHECK("FORM_BASE_INFO_CHECK", "单据基础信息校验", true, false, "", ""),

    /**
     * 单据关联费用校验
     */
    FORM_RELATE_EXPENSE_CHECK("FORM_RELATE_EXPENSE_CHECK", "单据关联费用校验", true, false, "", ""),

    /**
     * 单据单独提交校验
     */
    FORM_SINGLE_SUBMIT_CHECK("FORM_SINGLE_SUBMIT_CHECK", "单据单独提交校验", true, false, "", ""),

    /**
     * 单据行程关联校验
     */
    FORM_RELATE_TRAVEL_APPLY_CHECK("FORM_RELATE_TRAVEL_APPLY_CHECK", "单据行程关联校验", true, false, "", ""),

    /**
     * 单据发票多主体校验
     */
    FORM_INVOICE_MULTI_COMPANY_CHECK("FORM_INVOICE_MULTI_COMPANY_CHECK", "单据发票多主体校验", true, false, "报销单中多张发票有多个购方主体，请拆分提报", ""),

    /**
     * 单据报销金额校验
     */
    FORM_AMOUNT_CHECK("FORM_AMOUNT_CHECK", "单据报销金额校验", false, true, "", ""),

    /**
     * 单据提报人特征校验
     */
    FORM_CREATOR_FEATURE_CHECK("FORM_CREATOR_FEATURE_CHECK", "单据提报人特征校验", false, true, "", ""),

    /**
     * 单据扫码交单校验
     */
    FORM_SCAN_SUBMISSION_CHECK("FORM_SCAN_SUBMISSION_CHECK", "单据扫码交单校验", false, true, "", ""),
    /**
     * 财务打款校验
     */
    FORM_PAYMENT_CHECK("FORM_PAYMENT_CHECK", "财务打款校验", true, false, "", "%s"),
    ;

    // 校验项code
    private final String checkItemCode;
    // 校验项名称
    private final String checkItemName;
    // 是否阻止提交
    private final Boolean blockSubmit;
    // 是否需要人审
    private final Boolean needAudit;
    // 用户端-报错信息
    private final String userErrorInfo;
    // 审核端-审核信息
    private final String auditErrorInfo;

    public static CheckResultDetailDto getCheckResultDetail(ReimbursementFormCheckItemEnum checkItem) {
        return CheckResultDetailDto.builder()
                .checkItemCode(checkItem.getCheckItemCode())
                .checkItemName(checkItem.getCheckItemName())
                .blockSubmit(checkItem.getBlockSubmit())
                .needAudit(checkItem.getNeedAudit())
                .userErrorInfo(checkItem.getUserErrorInfo())
                .auditErrorInfo(checkItem.getAuditErrorInfo())
                .description(checkItem.getUserErrorInfo())
                .build();
    }
}
