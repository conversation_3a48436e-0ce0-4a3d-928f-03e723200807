package com.xhs.reimburse.enums.travel;

import com.xhs.reimburse.modal.dto.CheckResultDetailDto;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发票校验项目枚举类
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName InvoiceCheckItemEnum.java
 * @createTime 2025年03月18日 11:29:00
 */
@Getter
@AllArgsConstructor
public enum InvoiceCheckItemEnum {

    /**
     * 发票类型校验
     */
    INVOICE_TYPE_CHECK("INVOICE_TYPE_CHECK", "发票类型校验", true, false, "%s不支持上传该类型发票", ""),

    /**
     * 发票验真
     */
    INVOICE_AUTHENTICATION_CHECK("INVOICE_AUTHENTICATION_CHECK", "发票验真", true, false, "验真失败", ""),

    /**
     * 发票主体/税号校验
     */
    INVOICE_COMPANY_NAME_TAX_NUMBER_CHECK("INVOICE_COMPANY_NAME_TAX_NUMBER_CHECK","发票主体/税号校验", true, false, "发票主体/税号校验异常", ""),

    /**
     * 发票时间校验
     */
    INVOICE_TIME_CHECK("INVOICE_TIME_CHECK","发票时间校验", false, false, "开票日期不在关联差旅行程时间段内，建议在附件处上传支付截图等真实消费日期证明", ""),

    /**
     * 发票关联费用状态校验
     */
    INVOICE_RELATION_EXPENSE_STATUS_CHECK("INVOICE_RELATION_EXPENSE_STATUS_CHECK", "发票关联费用状态校验", true, false, "费用为待完善", ""),

    /**
     * 发票完整性校验
     */
    INVOICE_INTEGRITY_CHECK("INVOICE_INTEGRITY_CHECK", "发票完整性校验", true, false, "费用为待完善", ""),

    /**
     * 发票人工修改校验
     */
    INVOICE_MODIFY_CHECK("INVOICE_MODIFY_CHECK", "发票人工修改校验", false, true, "", "");

    // 校验项code
    private final String checkItemCode;
    // 校验项名称
    private final String checkItemName;
    // 是否阻止提交
    private final Boolean blockSubmit;
    // 是否需要人审
    private final Boolean needAudit;
    // 用户端-报错信息
    private final String userErrorInfo;
    // 审核端-审核信息
    private final String auditErrorInfo;

    public static CheckResultDetailDto getChckResultDetail(InvoiceCheckItemEnum checkItem) {
        return CheckResultDetailDto.builder()
                .checkItemCode(checkItem.getCheckItemCode())
                .checkItemName(checkItem.getCheckItemName())
                .blockSubmit(checkItem.getBlockSubmit())
                .needAudit(checkItem.getNeedAudit())
                .userErrorInfo(checkItem.getUserErrorInfo())
                .auditErrorInfo(checkItem.getAuditErrorInfo())
                .description(checkItem.getUserErrorInfo())
                .build();
    }
}
