package com.xhs.reimburse.enums.travel;

import com.xhs.reimburse.modal.dto.CheckResultDetailDto;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 费用校验项目枚举类
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ExpenseCheckItemEnum.java
 * @createTime 2025年03月18日 10:33:00
 */
@Getter
@AllArgsConstructor
public enum ExpenseCheckItemEnum {

    /**
     * 费用行程校验
     */
    EXPENSE_ITINERARY_CHECK("EXPENSE_ITINERARY_CHECK", "费用行程校验", true, false, "关联差旅申请中无%s行程", ""),

    /**
     * 费用地点校验
     */
    EXPENSE_LOCATION_CHECK("EXPENSE_LOCATION_CHECK", "费用地点校验", true, false, "关联差旅申请中无%s行程", ""),

    /**
     * 费用天数校验
     */
    EXPENSE_DURATION_CHECK("EXPENSE_DURATION_CHECK", "费用天数校验", true, false, "出差天数超出了关联申请的总天数", ""),

    /**
     * 费用状态校验
     */
    EXPENSE_STATUS_CHECK("EXPENSE_STATUS_CHECK", "费用状态校验", false, false, "该费用的tag为「待完善」", ""),

    /**
     * 发票验真
     */
    EXPENSE_INVOICE_VERIFICATION_CHECK("EXPENSE_INVOICE_VERIFICATION_CHECK", "发票验真", true, false, "存在验真失败的发票", ""),

    /**
     * 费用主体校验
     */
    EXPENSE_COMPANY_CHECK("EXPENSE_COMPANY_CHECK", "费用主体校验", true, false, "存在主体与报销单中付款公司不一致的发票", ""),

    /**
     * 费用开票时间校验
     */
    EXPENSE_INVOICE_DATE_CHECK("EXPENSE_INVOICE_DATE_CHECK", "费用开票时间校验", false, true, "存在开票时间异常发票", ""),

    /**
     * 费用出行时间校验
     */
    EXPENSE_TRAVEL_DATE_CHECK("EXPENSE_TRAVEL_DATE_CHECK", "费用出行时间校验", true, false, "火车票出行时间不在关联申请时间范围内，请关联正确申请单或补提申请单", ""),

    /**
     * 费用金额校验-油电费用
     */
    EXPENSE_EXPENSE_EXCEED_REIMBURSABLE_LIMIT("EXPENSE_EXPENSE_EXCEED_REIMBURSABLE_LIMIT", "费用金额校验-油电费用", true, false, "报销金额超出了可报销金额", ""),

    /**
     * 费用差标校验-火车票/飞机
     */
    EXPENSE_SEAT_STANDARD_DIFFERENCE_CHECK("EXPENSE_SEAT_STANDARD_DIFFERENCE_CHECK", "费用差标校验-火车票/飞机", false, false, "存在超标%s", ""),

    /**
     * 费用差标校验-其他
     */
    EXPENSE_COMMON_STANDARD_DIFFERENCE_CHECK("EXPENSE_COMMON_STANDARD_DIFFERENCE_CHECK", "费用差标校验-其他", false, false, "提报金额超标", ""),

    /**
     * 费用完整性校验
     */
    EXPENSE_INTEGRITY_CHECK("EXPENSE_INTEGRITY_CHECK", "费用完整性校验", true, false, "费用为待完善", ""),

    /**
     * 费用人审科目校验
     */
    EXPENSE_SUBJECT_NEED_AUDIT_CHECK("EXPENSE_SUBJECT_NEED_AUDIT_CHECK", "费用人审科目校验", false, true, "", ""),

    /**
     * 费用行程人工修改校验
     */
    EXPENSE_TRIP_MODIFY_CHECK("EXPENSE_TRIP_MODIFY_CHECK", "费用行程人工修改校验", false, true, "", ""),

    /**
     * 费用发票项目校验
     */
    EXPENSE_INVOICE_DETAIL_CHECK("EXPENSE_INVOICE_DETAIL_CHECK", "费用发票项目校验", false, true, "", "");

    // 校验项code
    private final String checkItemCode;
    // 校验项名称
    private final String checkItemName;
    // 是否阻止提交
    private final Boolean blockSubmit;
    // 是否需要人审
    private final Boolean needAudit;
    // 用户端-报错信息
    private final String userErrorInfo;
    // 审核端-审核信息
    private final String auditErrorInfo;

    public static CheckResultDetailDto getChckResultDetail(ExpenseCheckItemEnum checkItem) {
        return CheckResultDetailDto.builder()
                .checkItemCode(checkItem.getCheckItemCode())
                .checkItemName(checkItem.getCheckItemName())
                .blockSubmit(checkItem.getBlockSubmit())
                .needAudit(checkItem.getNeedAudit())
                .userErrorInfo(checkItem.getUserErrorInfo())
                .auditErrorInfo(checkItem.getAuditErrorInfo())
                .description(checkItem.getUserErrorInfo())
                .build();
    }

    /**
     * 座位超预算
     */
    public static boolean seatOverStandard(String code) {
        return EXPENSE_SEAT_STANDARD_DIFFERENCE_CHECK.getCheckItemCode().equals(code);
    }

    /**
     * 差标超预算
     */
    public static boolean overStandard(String code) {
        return EXPENSE_SEAT_STANDARD_DIFFERENCE_CHECK.getCheckItemCode().equals(code) || EXPENSE_COMMON_STANDARD_DIFFERENCE_CHECK.getCheckItemCode().equals(code);
    }

}
