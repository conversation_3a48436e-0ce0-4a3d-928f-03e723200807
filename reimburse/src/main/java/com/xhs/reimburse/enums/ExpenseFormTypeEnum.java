package com.xhs.reimburse.enums;

import com.google.common.collect.Lists;
import com.xhs.reimburse.modal.response.LabelValueExtendResponse;
import com.xhs.reimburse.modal.response.LabelValueResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ExpenseFormTypeEnum.java
 * @createTime 2025年02月25日 15:13:00
 */
@Getter
@AllArgsConstructor
public enum ExpenseFormTypeEnum {

    YBFY(ReimburseTypeEnum.YBFYBXD.getType(), ReimburseTypeEnum.YBFYBXD.getName(),
        Arrays.asList(
            ExpenseFirstSubjectEnum.YBFY_SNJT,
            ExpenseFirstSubjectEnum.YBFY_DAILY,
            ExpenseFirstSubjectEnum.YBFY_RECEPTION,
            ExpenseFirstSubjectEnum.YBFY_CONFERENCE,
            ExpenseFirstSubjectEnum.YBFY_EXT_AFFAIR,
            ExpenseFirstSubjectEnum.YBFY_GOODS,
            ExpenseFirstSubjectEnum.YBFY_PERSONNEL,
            ExpenseFirstSubjectEnum.YBFY_ADMIN,
            ExpenseFirstSubjectEnum.YBFY_OTHER
        )
    ),

    CL(ReimburseTypeEnum.CLBXD.getType(), ReimburseTypeEnum.CLBXD.getName(),
        Arrays.asList(
            ExpenseFirstSubjectEnum.CL_HOTEL,
            ExpenseFirstSubjectEnum.CL_LONG_TRIP,
            ExpenseFirstSubjectEnum.CL_BUSINESS_MEAL,
            ExpenseFirstSubjectEnum.CL_TRAIN_TICKET,
            ExpenseFirstSubjectEnum.CL_AIR_TICKET,
            ExpenseFirstSubjectEnum.CL_TAXI_TICKET,
            ExpenseFirstSubjectEnum.CL_DIDI,
            ExpenseFirstSubjectEnum.CL_BUS_TICKET,
            ExpenseFirstSubjectEnum.CL_FERRY_TICKET,
            ExpenseFirstSubjectEnum.CL_OVERSEAS_TRAFFIC,
            ExpenseFirstSubjectEnum.CL_FUEL_PARKING,
            ExpenseFirstSubjectEnum.CL_OTHER,
            ExpenseFirstSubjectEnum.CL_EXPRESS_RIDE_FEE,
            ExpenseFirstSubjectEnum.CL_FUEL_ELECTRIC_OTHER_FEE,
            ExpenseFirstSubjectEnum.CL_BUS_FERRY_TICKET
        )
    ),

    TJBXD(ReimburseTypeEnum.TJBXD.getType(), ReimburseTypeEnum.TJBXD.getName(),
        Lists.newArrayList(
            ExpenseFirstSubjectEnum.TJBXD_HOTEL,
            ExpenseFirstSubjectEnum.TJBXD_MEAL,
            ExpenseFirstSubjectEnum.TJBXD_TAXI_TICKET,
            ExpenseFirstSubjectEnum.TJBXD_DIDI,
            ExpenseFirstSubjectEnum.TJBXD_TRAIN_TICKET,
            ExpenseFirstSubjectEnum.TJBXD_AIR_TICKET,
            ExpenseFirstSubjectEnum.TJBXD_BUS_TICKET,
            ExpenseFirstSubjectEnum.TJBXD_FERRY_TICKET,
            ExpenseFirstSubjectEnum.TJBXD_FUEL_PARKING,
            ExpenseFirstSubjectEnum.TJBXD_CAR_RENTAL,
            ExpenseFirstSubjectEnum.TJBXD_ACTIVITIES,
            ExpenseFirstSubjectEnum.TJBXD_OTHER
        )
    ),

    YDFQBXD(ReimburseTypeEnum.YDFQBXD.getType(), ReimburseTypeEnum.YDFQBXD.getName(),
        Arrays.asList(
            ExpenseFirstSubjectEnum.YBFQBXD_TRAIN_TICKET,
            ExpenseFirstSubjectEnum.YBFQBXD_AIR_TICKET
        )
    ),
    
    ;

    // 单据类型
    private String formTypeCode;
    // 单据类型
    private String formTypeName;
    // 一级科目
    private List<ExpenseFirstSubjectEnum> firstSubjectEnums;

    public static List<LabelValueResponse> getFormTypes() {
        List<LabelValueResponse> formTypes = new ArrayList<>();
        for (ExpenseFormTypeEnum formType : ExpenseFormTypeEnum.values()) {
            LabelValueResponse response = new LabelValueResponse();
            response.setLabel(formType.getFormTypeName());
            response.setValue(formType.getFormTypeCode());
            response.setChildren(new ArrayList<>());
            formTypes.add(response);
        }
        return formTypes;
    }

    public static List<LabelValueExtendResponse> getSubjectsByFromType(String formTypeCode) {
        for (ExpenseFormTypeEnum formType : ExpenseFormTypeEnum.values()) {
            if (formType.getFormTypeCode().equals(formTypeCode)) {

                List<LabelValueExtendResponse> subjects = new ArrayList<>();
                // 一级科目
                for (ExpenseFirstSubjectEnum subject : formType.getFirstSubjectEnums()) {
                    LabelValueExtendResponse response = new LabelValueExtendResponse();
                    response.setLabel(subject.getSubjectName());
                    response.setValue(subject.getSubjectCode());
                    response.setDescription(subject.getDescription());
                    response.setValidFlag(subject.isEnableFlag());

                    // 二级科目
                    response.setChildren(ExpenseFirstSubjectEnum.getSubjectsByFromType(subject));
                    subjects.add(response);
                }
                return subjects;
            }
        }
        return Collections.emptyList();
    }
}
