package com.xhs.reimburse.enums;

import com.xhs.reimburse.modal.dto.InvoiceExceptionDto;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName InvoiceExceptionEnum.java
 * @createTime 2025年02月22日 16:24:00
 */
@AllArgsConstructor
@Getter
public enum InvoiceExceptionEnum {

    // OVERDUE_90_DAYS(1, "超90天", "发票已超过90天", "icon_overdue", "HIGH"),
    COMPANY_NAME_TAX_NUMBER_ERROR(2, "主体/税号异常", "该发票的购方主体/税号不在公司范围内，请重新开票，否则无法提报。\n你的开票主体&税号为：\n主体：%s\n税号：%s", "icon_mismatch", "MEDIUM","主体/税号异常"),
    AUTHENTICATION_FAILED(3, "验真失败", "该张发票验真失败，不可提报", "icon_auth_failed", "HIGH","验真失败");

    private final Integer exceptionCode;
    private final String exceptionName;
    private final String exceptionDes;
    private final String exceptionIcon;
    private final String exceptionLevel;
    private final String exceptionTitle;

    public static InvoiceExceptionDto getInvoiceExceptionDto(InvoiceExceptionEnum invoiceExceptionEnum) {
        InvoiceExceptionDto exceptionDto = new InvoiceExceptionDto();
        exceptionDto.setExceptionCode(invoiceExceptionEnum.getExceptionCode());
        exceptionDto.setExceptionName(invoiceExceptionEnum.getExceptionName());
        exceptionDto.setExceptionIcon(invoiceExceptionEnum.getExceptionIcon());
        exceptionDto.setExceptionLevel(invoiceExceptionEnum.getExceptionLevel());
        exceptionDto.setExceptionDes(invoiceExceptionEnum.getExceptionDes());
        exceptionDto.setExceptionTitle(invoiceExceptionEnum.getExceptionTitle());
        return exceptionDto;
    }
}
