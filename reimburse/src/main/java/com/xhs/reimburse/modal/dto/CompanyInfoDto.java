package com.xhs.reimburse.modal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date :2025/07/31 - 上午11:06
 * @description :
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompanyInfoDto {
    private String businessCode;
    private String companyCode;
    private String companyName;
    private String taxNumber;
    private String financeCode;
    private String isAbroadCompany;
    private String isDefault;
    private String verifyStatus;
}
