package com.xhs.reimburse.modal.dto;

import com.xhs.reimburse.enums.ReimbursementFormCheckItemEnum;
import com.xhs.reimburse.enums.travel.ExpenseCheckItemEnum;
import com.xhs.reimburse.enums.travel.InvoiceCheckItemEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/07/08 - 下午5:01
 * @description :校验结果详情
 * @see ReimbursementFormCheckItemEnum
 * @see ExpenseCheckItemEnum
 * @see InvoiceCheckItemEnum
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CheckResultDetailDto {
    /**
     * 校验项code
     */
    private String checkItemCode;

    /**
     * 校验项名称
     */
    private String checkItemName;

    /**
     * 是否阻碍提报
     * true: 阻碍提报
     * false: 不阻碍提报
     */
    @Builder.Default
    private Boolean blockSubmit = false;

    /**
     * 是否需要人审
     * true: 需要人审
     * false: 不需要人审
     */
    @Builder.Default
    private Boolean needAudit = false;

    /**
     * 用户端-报错信息
     */
    private String userErrorInfo;

    /**
     * 审核端-报错信息
     */
    private String auditErrorInfo;

    // 阻碍项列表
    @Builder.Default
    private List<BlockItemDto> blockItemList = new ArrayList<>();

    // 兼容处理
    private String description;
}
