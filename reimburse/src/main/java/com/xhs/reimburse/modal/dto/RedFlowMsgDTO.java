package com.xhs.reimburse.modal.dto;

import com.xiaohongshu.fls.rpc.workflow.v2.task.resp.CurrentTaskInfo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

// red flow 消息dto
@Data
public class RedFlowMsgDTO {
    private String formNo;

    private String formType;

    private String auditStatus;

    private String auditPhaseKey;

    private String auditPhaseId;

    private String auditPhase;

    private Boolean processEnd;

    private String formContent;

    private List<CurrentTaskInfo> currentTaskInfo;

    private String subject;

    private int paymentType;

    private String payee;

    private String recipientAccount;

    private String currency;

    private BigDecimal amount;

    private String recipientBank;

    private String paymentCompanyName;
}
