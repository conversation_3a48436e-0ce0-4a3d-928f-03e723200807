package com.xhs.reimburse.modal.dto;

import cn.hutool.core.collection.CollUtil;
import com.xhs.reimburse.constant.ErrorConstant;
import com.xhs.reimburse.enums.ExpenseStatusEnum;
import com.xhs.reimburse.modal.dto.travel.ItineraryCheckResultDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date :2025/02/07 - 下午8:39
 * @description : 费用明细
 */
@Data
public class ExpenseDto {

    @ApiModelProperty("自增 ID")
    private Long id;

    @ApiModelProperty("费用uuid")
    private String uuid;

    @ApiModelProperty("所属单据")
    private String formType;

    @ApiModelProperty("一级科目")
    private String firstSubject;

    @ApiModelProperty("二级科目")
    private String secondSubject;

    @ApiModelProperty("一级科目名称")
    private String firstSubjectName;

    @ApiModelProperty("二级科目名称")
    private String secondSubjectName;

    @ApiModelProperty("发票创建人userId")
    private String creatorUserId;

    @ApiModelProperty("逻辑删除标记，0：已删除，1：未删除")
    private Integer status;

    @ApiModelProperty("费用状态 1：可提报 2:待补充 3:审核中 4: 待打款 5:已报销")
    private Integer expenseStatus;

    @ApiModelProperty("发票来源，1:控台 2:Bot")
    private Integer expenseSource;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("动态表单内容")
    private List<DynamicFormFieldDto> dynamicFormFieldDtoList;

    @ApiModelProperty("关联发票详情")
    private List<InvoiceDto> relationInvoiceList;

    @ApiModelProperty("附件")
    private List<FileInfoDto> uploadFileInfoList;

    @ApiModelProperty("关联发票数量")
    private Integer relationInvoiceNumber;

    @ApiModelProperty("附件数量")
    private Integer uploadFileNumber;

    @ApiModelProperty("报销金额")
    private BigDecimal amount;

    @ApiModelProperty("费用说明")
    private String costComment;

    @ApiModelProperty("消费日期")
    private String datePhase;

    @ApiModelProperty("超标费用说明Map")
    private Map<String, String> exceptionDetailsMap = new HashMap<>(16);

    @ApiModelProperty("费用超标信息")
    private String beyondStandardInfo = "";

    private Integer expenseInvoiceStatus;
    private Integer expenseFormStatus;

    @ApiModelProperty("发票状态number")
    private Integer expenseStatusShowNumber;

    @ApiModelProperty("发票状态名称")
    private String expenseStatusShowName;

    @ApiModelProperty("关联发票是否修改")
    private Boolean relationInvoicesHasModified;

    @ApiModelProperty("关联单据uuIdList")
    private List<String> relationFormUuidList;

    @ApiModelProperty("关联单据formNum")
    private String relationFormNum;

    @ApiModelProperty("费用校验信息")
    private ExpenseCheckDto expenseCheckDto;

    @ApiModelProperty("差旅工作餐: 超标理由")
    private TravelMealOverLimitReasonDto travelMealOverLimitReasonDto;

    @ApiModelProperty("校验结果")
    private ItineraryCheckResultDto itineraryCheckResultDto;

    @ApiModelProperty("费用关联发票的开始时间")
    private Date relationInvoiceStartTime;

    @ApiModelProperty("费用关联发票的结束时间")
    private Date relationInvoiceEndTime;

    public String getExpenseSubjectName(){
        if(StringUtils.isEmpty(this.getFirstSubjectName())){
            return uuid;
        }

        if(StringUtils.isEmpty(this.getSecondSubjectName())){
            return this.getFirstSubjectName();
        }else {
            return String.format("%s-%s", this.getFirstSubjectName(), this.getSecondSubjectName());
        }
    }

    /**
     * 费用是否阻碍提交，阻碍则返回阻碍原因
     *
     * @return notBlank: 阻碍提交 isBlank: 不阻碍提交
     */
    public String expenseBlockSubmitAlert() {

        //费用的状态不可提交状态
        if (!ExpenseStatusEnum.checkExpenseCanSubmit(this)) {
            return ErrorConstant.EXPENSE_SUBMIT_COMPLETE;
        }

        //费用是否存在阻碍项
        if (Objects.nonNull(itineraryCheckResultDto)
                && CollUtil.isNotEmpty(itineraryCheckResultDto.getBlockSubmissionCheckResultList())) {
            return itineraryCheckResultDto.getBlockSubmissionCheckResultList().get(0).getDescription();
        }

        return "";
    }
}