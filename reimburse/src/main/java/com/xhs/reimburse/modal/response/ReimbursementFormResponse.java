package com.xhs.reimburse.modal.response;

import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.FileInfoDto;
import com.xhs.reimburse.modal.dto.ReimburseBasicInfo;
import com.xhs.reimburse.modal.dto.travel.ItineraryCheckResultDto;
import com.xhs.reimburse.modal.entity.EmployeeEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "报销单详情公共字段")
public class ReimbursementFormResponse {

    @ApiModelProperty(notes = "报销表单主键")
    private Long id;

    @ApiModelProperty(notes = "报销表单唯一键")
    private String uuid;

    @ApiModelProperty(notes = "金额")
    private BigDecimal amount;

    @ApiModelProperty(notes = "单据号")
    private String formNum;

    @NotBlank(message = "当前单据类型不能为空")
    @ApiModelProperty(notes = "单据类型")
    private String formType;

    @ApiModelProperty(notes = "费用编号")
    private List<String> expenseNos;

    @ApiModelProperty(notes = "费用信息")
    private List<ExpenseDto> expenses;

    @ApiModelProperty(notes = "基础报销信息")
    @NotNull(message = "基础报销信息不能为空")
    private ReimburseBasicInfo reimburseBasicInfoVo;

    @ApiModelProperty(notes = "校验状态")
    private Integer checkStatus;

    @ApiModelProperty(notes = "报销单状态")
    private Integer reimburseStatus;

    @ApiModelProperty(notes = "附件")
    private List<FileInfoDto> fileAttachmentList;

    @ApiModelProperty(notes = "创建人员工编号")
    private String creatorNo;

    @ApiModelProperty(notes = "创建人员工信息")
    private EmployeeEntity creatorInfo;

    @ApiModelProperty(notes = "单据创建时间")
    private String createTime;

    @ApiModelProperty(notes = "打印类型")
    private String formAuditType;

    @ApiModelProperty(notes = "单据可以编辑标志为 0 不可编辑 1 可编辑")
    private Boolean canEdit;

    @ApiModelProperty(notes = "是否为草稿状态")
    private Boolean isDraft;

    @ApiModelProperty("校验结果")
    private ItineraryCheckResultDto itineraryCheckResultDto;
}
