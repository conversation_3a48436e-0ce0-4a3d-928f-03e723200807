package com.xhs.reimburse.modal.dto;

import com.xhs.reimburse.modal.dto.CheckResultDetailDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName FileInfoDto.java
 * @createTime 2025年02月13日 17:13:00
 */
@Data
@NoArgsConstructor
public class FileInfoDto {

    @NotNull(message = "文件id不能为空")
    @ApiModelProperty("文件id")
    private String fileId;

    @NotNull(message = "business不能为空")
    @ApiModelProperty("business")
    private String business;

    @NotNull(message = "scene不能为空")
    @ApiModelProperty("scene")
    private String scene;

    @ApiModelProperty("name")
    private String name;

    @ApiModelProperty("mimeType")
    private String mimeType;

    @ApiModelProperty("可预览/下载的URL")
    private String url;

    @ApiModelProperty("解密后的fileId")
    private String decryptedFileId;

    @ApiModelProperty("文件校验结果列表")
    private List<CheckResultDetailDto> checkResultItemList = new ArrayList<>();

    public FileInfoDto(String fileId, String business, String scene, String name) {
        this.fileId = fileId;
        this.business = business;
        this.scene = scene;
        this.name = name;
    }
}
