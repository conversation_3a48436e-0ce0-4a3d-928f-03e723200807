package com.xhs.reimburse.modal.dto;

import cn.hutool.core.util.StrUtil;
import com.xhs.reimburse.constant.ErrorConstant;
import com.xhs.reimburse.enums.InvoiceExceptionEnum;
import com.xhs.reimburse.modal.dto.travel.ItineraryCheckResultDto;
import com.xhs.reimburse.utils.NewBeeStringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/02/08 - 上午11:20
 * @description : 发票
 */
@Data
public class InvoiceDto {

    @ApiModelProperty("发票ID")
    private Long id;

    @ApiModelProperty("发票UUID")
    private String uuid;

    @ApiModelProperty("票据类型")
    private String ticketType;

    @ApiModelProperty("发票号码")
    private String invoiceNo;

    @ApiModelProperty("发票代码")
    private String invoiceCode;

    @ApiModelProperty("发票类型")
    private String invoiceType;

    @ApiModelProperty("验真结果")
    private Integer invoiceValidateResult;

    @ApiModelProperty("验真失败原因")
    private String invoiceValidateFailReason;

    @ApiModelProperty("发票状态")
    private Integer invoiceStatus;

    @ApiModelProperty("状态 1:有效 0:已删除")
    private Integer status;

    @ApiModelProperty("发票创建人userId")
    private String creatorUserId;

    @ApiModelProperty("发票类型Tag")
    private String invoiceTypeTag;

    @ApiModelProperty("异常状态列表")
    private List<InvoiceExceptionDto> exceptionList;

    @ApiModelProperty("发票来源")
    private Integer invoiceSource;

    @ApiModelProperty("文件信息")
    private FileInfoDto uploadFileInfo;

    @ApiModelProperty("动态表单内容")
    private List<DynamicFormFieldDto> dynamicFormFieldDtoList;

    @ApiModelProperty("ocr识别内容是否修改 1: 未修改 2:已修改")
    private Integer ocrContentModified;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("开票日期")
    private String invoiceDate;

    @ApiModelProperty("购方税号")
    private String purchaserTaxNo;

    @ApiModelProperty("购方名称")
    private String purchaserName;

    @ApiModelProperty("校验码")
    private String checkCode;

    @ApiModelProperty("ocr解析内容")
    private String ocrSourceContent;

    @ApiModelProperty("关联的费用uuIds")
    private List<String> relationExpenseUuids;

    private Integer invoiceExpenseStatus;
    private Integer invoiceFormStatus;

    @ApiModelProperty("发票状态number")
    private Integer invoiceStatusShowNumber;

    @ApiModelProperty("发票状态名称")
    private String invoiceStatusShowName;

    @ApiModelProperty("校验结果")
    private ItineraryCheckResultDto itineraryCheckResultDto;


    /**
     * 发票是否验真成功
     * true: 验真成功、不需要验真
     */
    public boolean invoiceValidateSuccess() {
        if (CollectionUtils.isNotEmpty(exceptionList)) {
            return exceptionList.stream()
                    .noneMatch(i -> InvoiceExceptionEnum.AUTHENTICATION_FAILED.getExceptionCode().equals(i.getExceptionCode()));
        }
        return true;
    }

    /**
     * 购方主体提交阻碍校验
     */
    public boolean purchaserNameCheck(String targetCompanyName) {
        //购方不一致
        if (StrUtil.isNotBlank(purchaserName) && StrUtil.isNotBlank(targetCompanyName)) {
            return NewBeeStringUtil.ignoreBracketEquals(purchaserName, targetCompanyName);
        }
        return true;
    }

    /**
     * 发票是否阻碍提交，阻碍则返回阻碍原因
     *
     * @param targetCompanyName 付款公司
     * @return notBlank: 阻碍提交 isBlank: 不阻碍提交
     */
    public String invoiceBlockSubmitAlert(String targetCompanyName) {

        if (!invoiceValidateSuccess()) {
            return ErrorConstant.INVOICE_SUBMIT_VALIDATE;
        }

        if (!purchaserNameCheck(targetCompanyName)) {
            return ErrorConstant.INVOICE_BUYER_CHECK;
        }

        return "";
    }
}
