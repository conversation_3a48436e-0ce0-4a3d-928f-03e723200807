package com.xhs.reimburse.modal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/07/08 - 下午4:58
 * @description :
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReimbursementFormCheckResultDto {
    // 校验是否通过
    private Boolean checkPass;

    // 是否需要审核
    private Boolean needAudit;

    // 校验结果详情列表
    @Builder.Default
    List<CheckResultDetailDto> checkResultDetailList = new ArrayList<>();
}
