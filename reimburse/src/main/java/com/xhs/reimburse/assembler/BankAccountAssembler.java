package com.xhs.reimburse.assembler;

import cn.hutool.core.util.StrUtil;
import com.xhs.ehr.rpc.response.EmployeeInfo;
import com.xhs.reimburse.assembler.mapper.BankAccountDtoMapper;
import com.xhs.reimburse.modal.dto.BankAccountDto;
import com.xhs.reimburse.modal.entity.BankAccountEntity;
import com.xhs.reimburse.modal.request.BankAccountRequest;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class BankAccountAssembler {
    @Resource
    private BankAccountDtoMapper bankAccountDtoMapper;
    
    public BankAccountDto toDto(BankAccountEntity entity) {
        BankAccountDto dto = bankAccountDtoMapper.toDto(entity);
        return dto;
    }

    public List<BankAccountDto> toDtoList(List<BankAccountEntity> entityList) {
        List<BankAccountDto> dtoList = bankAccountDtoMapper.toDtoList(entityList);
        return dtoList;
    }

    public BankAccountEntity toEntity(BankAccountDto dto) {
        BankAccountEntity entity = bankAccountDtoMapper.toEntity(dto);
        return entity;
    }

    public List<BankAccountEntity> toEntityList(List<BankAccountDto> dtoList) {
        List<BankAccountEntity> entityList = bankAccountDtoMapper.toEntityList(dtoList);
        return entityList;
    }

    public BankAccountEntity reqToEntity(BankAccountRequest request,String userId) {
        if (Objects.nonNull(request)) {
            BankAccountEntity entity = new BankAccountEntity();
            entity.setId(request.getId());
            entity.setAccountNo(request.getAccountNo().replaceAll(" ",""));
            entity.setBankCode(request.getBankCode());
            entity.setBankName(request.getBankName());
            entity.setAccountName(request.getAccountName());
            entity.setIsValid(request.getIsValid());
            entity.setIsDefault(request.getIsDefault());
            entity.setUserId(userId);
            return entity;
        }
        return null;
    }

    public BankAccountEntity toBankAccountEntity(EmployeeInfo employeeInfo, String bankCode) {
        BankAccountEntity bankAccount = new BankAccountEntity();
        bankAccount.setUserId(String.valueOf(employeeInfo.getEmployeeId()));
        bankAccount.setBankName(employeeInfo.getBankName());
        bankAccount.setBankCode(StrUtil.blankToDefault(bankCode, ""));
        bankAccount.setAccountName(employeeInfo.getUserName());
        bankAccount.setAccountNo(employeeInfo.getBankNum());
        bankAccount.setIsDefault(1);
        bankAccount.setIsValid(StrUtil.isBlank(bankCode) ? 0 : 1);
        return bankAccount;
    }
}
