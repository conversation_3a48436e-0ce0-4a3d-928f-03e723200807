package com.xhs.reimburse.rpc.consumer.ocr;

import com.alibaba.fastjson2.JSON;
import com.google.common.base.Stopwatch;
import com.xhs.reimburse.enums.InvoiceExceptionEnum;
import com.xhs.reimburse.enums.InvoiceTypeEnum;
import com.xhs.reimburse.enums.InvoiceValidateEnum;
import com.xhs.reimburse.enums.OcrBatchInvoiceTypeEnum;
import com.xhs.reimburse.modal.dto.InvoiceDto;
import com.xhs.reimburse.rpc.consumer.ocr.bean.ImageInvoicesRecogcollectMediaInvoice;
import com.xhs.reimburse.rpc.consumer.ocr.bean.ImageInvoicesRecogcollectResponse;
import com.xiaohongshu.erp.common.exception.BusinessException;
import com.xiaohongshu.fls.rpc.oacontract.baiwang.response.InterfaceResponse;
import com.xiaohongshu.fls.rpc.oacontract.baiwang.service.ExternalInvoiceService;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OcrRpcService.java
 * @createTime 2025年02月19日 17:17:00
 */
@Slf4j
@Service
public class OcrRpcService {

    @Resource
    private ExternalInvoiceService.Iface externalInvoiceService;

    /**
     * 批量OCR识别
     *
     * @param url
     * @return
     */
    public InterfaceResponse batchOcrParse(String url) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("batchOcrParse start url:{}", url);

        InterfaceResponse response = null;
        ImageInvoicesRecogcollectResponse ocrResponse = null;
        try {
            response = externalInvoiceService.ocrRecogcollect(new Context(), Arrays.asList(url));
            log.info("batchOcrParse response.  url:{} response:{}", url, response);
            log.info("batchOcrParse 接口调用时间为：{} ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

            if (!response.isSuccess()) {
                log.error("batchOcrParse error:url:{},response:{}", url, response);
                throw new BusinessException("OCR批量识别失败", response.message);
            }
            return response;
        } catch (Exception e) {
            log.error("batchOcrParse error:url:{}", url, e);
            throw new BusinessException("OCR批量识别失败", e.getMessage());
        }
    }

    public InterfaceResponse ocrSandTickets(String url) {
        InterfaceResponse response;
        try {
            response = externalInvoiceService.ocrSandTickets(new Context(), url);
            if (!response.success) {
                log.error("ocrSandTickets error,url:{},response:{}", url, response);
                throw new BusinessException("OCR批量解析异常", response.getMessage());
            }
            log.info("ocrSandTickets success. urls:{},response:{}", url, response);
            return response;
        } catch (Exception e) {
            log.error("OCR识别接口调用异常,异常信息：{}", e.getMessage(), e);
            throw new BusinessException("OCR解析异常", e.getMessage());
        }
    }

    public void invoiceValidate(InvoiceDto invoiceDto) {
        // 无需校验
        if (StringUtils.isNotEmpty(invoiceDto.getTicketType()) && InvoiceTypeEnum.getNotNeedValidateTicketTypeList().contains(invoiceDto.getTicketType())) {
            invoiceDto.setInvoiceValidateFailReason("");
            invoiceDto.setInvoiceValidateResult(InvoiceValidateEnum.NO_NEED_VALIDATE.getCode());
            return;
        }

        // 发票原数据
        String ocrSourceContent = invoiceDto.getOcrSourceContent();
        ImageInvoicesRecogcollectMediaInvoice sourceInvoice = JSON.parseObject(ocrSourceContent, ImageInvoicesRecogcollectMediaInvoice.class);

        // 税号
        List<String> amountTaxCodeList = OcrBatchInvoiceTypeEnum.needResetPurchaserTaxNoCodeList();
        String purchaserTaxNo = invoiceDto.getPurchaserTaxNo();
        if(amountTaxCodeList.contains(invoiceDto.getInvoiceType())){
            purchaserTaxNo = sourceInvoice.getPurchaserTaxNo();
        }

        // 校验码
        String checkCode = invoiceDto.getCheckCode();
        // 发票类型 为86-数电纸质发票（普通发票）时填写20位数电票号码的后6位；
        if (OcrBatchInvoiceTypeEnum.DIGITAL_PAPER_VAT_ORDINARY.getCode().equals(invoiceDto.getInvoiceType())) {
            if (!StringUtils.isEmpty(checkCode) && checkCode.length() >= 6) {
                checkCode = checkCode.substring(checkCode.length() - 6); // 取后6位
            } else {
                log.warn("发票号码长度不足 6 位，无法获取后 6 位: {}", checkCode);
            }
        }

        // 开票日期
        String invoiceDate = invoiceDto.getInvoiceDate();
        if (OcrBatchInvoiceTypeEnum.DIGITAL_RAIL_TICKET.getCode().equals(invoiceDto.getInvoiceType()) ||
                OcrBatchInvoiceTypeEnum.DIGITAL_AIR_ITINERARY.getCode().equals(invoiceDto.getInvoiceType())) {
            invoiceDate = sourceInvoice.getInvoiceDate();
        }

        // 发票号码、开票日期、购方税号 必填
        if (StringUtils.isEmpty(invoiceDate) || StringUtils.isEmpty(invoiceDto.getInvoiceNo()) ||
                StringUtils.isEmpty(purchaserTaxNo)) {
            invoiceDto.setInvoiceValidateFailReason("发票号码、开票日期、购方税号 不能为空");
            invoiceDto.setInvoiceValidateResult(InvoiceValidateEnum.VALIDATE_FAIL.getCode());
            invoiceDto.getExceptionList().add(
                    InvoiceExceptionEnum.getInvoiceExceptionDto(InvoiceExceptionEnum.AUTHENTICATION_FAILED)
            );
            return;
        }

        InterfaceResponse interfaceResponse = null;
        try {

            String totalAmount = invoiceDto.getAmount().toString();
            interfaceResponse = externalInvoiceService.invoiceValidate(new Context(), invoiceDate, totalAmount
                    , invoiceDto.getInvoiceNo(), invoiceDto.getPurchaserTaxNo(), invoiceDto.getInvoiceCode(), checkCode);

            log.info("OcrRpcService.invoiceCheck.invoiceDto:{},interfaceResponse:{}", invoiceDto, interfaceResponse);

            if (interfaceResponse == null || !interfaceResponse.success) {
                invoiceDto.setInvoiceValidateFailReason(interfaceResponse == null ? "百旺验真返回异常" : interfaceResponse.getMessage());
                invoiceDto.setInvoiceValidateResult(InvoiceValidateEnum.VALIDATE_FAIL.getCode());
            } else {
                // 成功
                invoiceDto.setInvoiceValidateFailReason("");
                invoiceDto.setInvoiceValidateResult(InvoiceValidateEnum.VALIDATE_SUCCESS.getCode());
            }
        } catch (TException e) {

            log.error("OcrRpcService.invoiceCheck error. invoiceDto:{}", invoiceDto, e);

            invoiceDto.setInvoiceValidateFailReason(e.getMessage());
            invoiceDto.setInvoiceValidateResult(InvoiceValidateEnum.VALIDATE_FAIL.getCode());
        }

        // 将异常信息落库
        if (InvoiceValidateEnum.VALIDATE_FAIL.getCode() == invoiceDto.getInvoiceValidateResult()) {
            invoiceDto.getExceptionList().add(
                    InvoiceExceptionEnum.getInvoiceExceptionDto(InvoiceExceptionEnum.AUTHENTICATION_FAILED)
            );
        }
    }
}
