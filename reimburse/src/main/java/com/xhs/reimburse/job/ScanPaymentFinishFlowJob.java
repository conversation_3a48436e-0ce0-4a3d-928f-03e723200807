package com.xhs.reimburse.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xhs.reimburse.service.external.redflow.RedFlowRpcService;
import com.xhs.reimburse.xhsoa.mapper.FormPaymentDetailMapper;
import com.xhs.reimburse.xhsoa.modal.FormPaymentDetail;
import com.xiaohongshu.infra.redschedule.api.RedSchedule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.xhs.reimburse.mq.consumer.PaymentMessageProcessor.PAY_SUCCESS;

/**
 * 扫描打款成功超过15天的单据设置单据完成
 */
@Component
@Slf4j
public class ScanPaymentFinishFlowJob {

    // 系统用户ID，用于完成流程任务
    private static final String SYSTEM_USER_ID = "系统";
    @Resource
    private FormPaymentDetailMapper formPaymentDetailMapper;
    @Resource
    private RedFlowRpcService redFlowRpcService;

    @RedSchedule(value = "oaoffice.ScanPaymentFinishFlowJob", cron = "0 0 1 * * ?", desc = "扫描打款完成超过15天的单据设置单据完成")
    public void execute(){
        log.info("ScanPaymentFinishFlowJob - 开始扫描打款完成超过15天的单据");
        try {
            // 计算15天前的日期
            Date now = new Date();
            Date fifteenDaysAgo = DateUtils.addDays(now, -15);
            // 查询支付状态为SUCCESS，且支付时间超过15天的记录
            QueryWrapper<FormPaymentDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("payment_status", PAY_SUCCESS)
                    .lt("payment_success_time", fifteenDaysAgo)
                    .eq("is_valid", 1);
            List<FormPaymentDetail> paymentDetails = formPaymentDetailMapper.selectList(queryWrapper);
            log.info("ScanPaymentFinishFlowJob - 查询到 {} 条符合条件的支付记录", paymentDetails.size());
            for (FormPaymentDetail paymentDetail : paymentDetails) {
                try {
                    String formNum = paymentDetail.getFormNo();
                    if (formNum == null || formNum.trim().isEmpty()) {
                        log.warn("ScanPaymentFinishFlowJob - 单据号为空，跳过处理，paymentNo: {}", paymentDetail.getPaymentNo());
                        continue;
                    }
                    log.info("ScanPaymentFinishFlowJob - 开始完成单据流程，formNum: {}, paymentNo: {}", formNum, paymentDetail.getPaymentNo());
                    // 调用RedFlowRpcService完成单据流程
                    redFlowRpcService.completeTask(SYSTEM_USER_ID, formNum);
                    log.info("ScanPaymentFinishFlowJob - 单据流程完成成功，formNum: {}", formNum);
                } catch (Exception e) {
                    log.error("ScanPaymentFinishFlowJob - 完成单据流程失败，formNum: {}, paymentNo: {}, error: {}", paymentDetail.getFormNo(), paymentDetail.getPaymentNo(), e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("ScanPaymentFinishFlowJob - 扫描打款完成单据异常: {}", e.getMessage(), e);
        }
    }
}
