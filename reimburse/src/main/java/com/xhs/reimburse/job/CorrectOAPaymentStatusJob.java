package com.xhs.reimburse.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xhs.reimburse.enums.ReimbursementFormStatusEnum;
import com.xhs.reimburse.mapper.TReimbursementFormMapper;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.modal.entity.ReimbursementFormExample;
import com.xhs.reimburse.service.external.finance.CashierRpcService;
import com.xhs.reimburse.xhsoa.mapper.FormPaymentDetailMapper;
import com.xhs.reimburse.xhsoa.modal.FormPaymentDetail;
import com.xiaohongshu.fls.rpc.finance.cashiercenter.model.PaymentQueryBySourceNoResponse;
import com.xiaohongshu.infra.redschedule.api.RedSchedule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 与付款做对账，保持付款状态一致
 */
@Component
@Slf4j
public class CorrectOAPaymentStatusJob {

    @Resource
    private TReimbursementFormMapper reimbursementFormMapper;

    @Resource
    private FormPaymentDetailMapper formPaymentDetailMapper;

    @Resource
    private CashierRpcService cashierRpcService;

    @RedSchedule(value = "oaoffice.CorrectOAPaymentStatusJob", cron = "0 0 23 * * ?", desc = "与付款单做对账，保证本地付款状态的正确性")
    public void execute() {
        log.info("开始执行付款状态对账任务");
        try {
            // 1. 查看reimbursement_form表中过去15天内的，审核中的全部报销单
            List<ReimbursementFormEntity> auditForms = getAuditFormsInLast15Days();
            log.info("查询到过去15天内审核中的报销单数量: {}", auditForms.size());
            for (ReimbursementFormEntity form : auditForms) {
                try {
                    correctPaymentStatus(form);
                } catch (Exception e) {
                    log.error("处理报销单 {} 时发生错误: {}", form.getFormNum(), e.getMessage(), e);
                }
            }
            log.info("付款状态对账任务执行完成，处理报销单总数: {}", auditForms.size());
        } catch (Exception e) {
            log.error("付款状态对账任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取过去15天内审核中的报销单
     */
    private List<ReimbursementFormEntity> getAuditFormsInLast15Days() {
        // 计算15天前的日期
        Date now = new Date();
        Date fifteenDaysAgo = DateUtils.addDays(now, -15);
        // 构建查询条件
        ReimbursementFormExample example = new ReimbursementFormExample();
        example.createCriteria().andReimburseStatusEqualTo(ReimbursementFormStatusEnum.SHZ.getCode()) // 审核中状态
                .andSubmitTimeGreaterThanOrEqualTo(fifteenDaysAgo).andIsValidEqualTo(1); // 有效记录
        return reimbursementFormMapper.selectByExample(example);
    }

    /**
     * 根据formnum查询form_payment_detail表，获取最新的一条记录
     */
    private FormPaymentDetail getLatestPaymentDetail(String formNum) {
        QueryWrapper<FormPaymentDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("form_no", formNum).eq("is_valid", 1).orderByDesc("id").last("LIMIT 1");
        return formPaymentDetailMapper.selectOne(queryWrapper);
    }

    /**
     * 修正单个报销单的付款状态
     */
    private void correctPaymentStatus(ReimbursementFormEntity form) {
        String formNum = form.getFormNum();
        log.info("开始处理报销单: {}", formNum);
        // 2. 根据formnum查询form_payment_detail表，获取最新的一条记录
        FormPaymentDetail latestPaymentDetail = getLatestPaymentDetail(formNum);
        if (latestPaymentDetail == null) {
            log.info("报销单 {} 没有找到付款详情记录", formNum);
            return;
        }
        String paymentNo = latestPaymentDetail.getPaymentNo();
        if (paymentNo == null || paymentNo.trim().isEmpty()) {
            log.info("报销单 {} 的付款编号为空", formNum);
            return;
        }
        // 3. 获取payment_no，作为参数请求OAPaymentService.queryPaymentResult
        try {
            PaymentQueryBySourceNoResponse response = cashierRpcService.queryPaymentResult(paymentNo);
            if (response == null) {
                log.warn("报销单 {} 查询付款结果返回空", formNum);
                return;
            }
            // 4. 根据返回结果获取status字段
            String actualStatus = response.getStatus();
            String localStatus = latestPaymentDetail.getPaymentStatus();
            log.info("报销单 {} 本地状态: {}, 实际状态: {}", formNum, localStatus, actualStatus);
            // 5. 如果status与form_payment_detail记录的payment_status是否一致，不一致则更新payment_status字段
            if (actualStatus != null && !actualStatus.equals(localStatus)) {
                log.info("报销单 {} 付款状态不一致，本地状态: {}, 实际状态: {}, 开始更新", formNum, localStatus, actualStatus);
                // 更新付款状态
                formPaymentDetailMapper.updatePaymentStatus(paymentNo, actualStatus, "系统自动对账修正");
            }
        } catch (Exception e) {
            log.error("报销单 {} 查询付款结果时发生异常: {}", formNum, e.getMessage(), e);
        }
    }
}
