package com.xhs.reimburse.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xhs.reimburse.constant.CommonConstant;
import com.xhs.reimburse.enums.ExpenseSecondSubjectEnum;
import com.xhs.reimburse.enums.InvoiceTypeEnum;
import com.xhs.reimburse.enums.OcrBatchInvoiceTypeEnum;
import com.xhs.reimburse.enums.PlaneCabinTypeEnum;
import com.xhs.reimburse.modal.dto.DynamicFormFieldDto;
import com.xhs.reimburse.modal.dto.DynamicFormFieldListQueryDto;
import com.xhs.reimburse.modal.dto.TripRouteCityDto;
import com.xhs.reimburse.modal.entity.EmployeeEntity;
import com.xhs.reimburse.modal.response.LabelValueResponse;
import com.xhs.reimburse.rpc.consumer.ocr.OcrInvoiceDataResponse;
import com.xhs.reimburse.rpc.consumer.ocr.bean.ImageInvoicesRecogcollectMediaInvoice;
import com.xhs.reimburse.service.DynamicFormFieldService;
import com.xhs.reimburse.service.ExpenseService;
import com.xhs.reimburse.service.ReimburseCommonService;
import com.xhs.reimburse.service.external.ehr.EhrEmployeeRpcService;
import com.xhs.reimburse.utils.StreamUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DynamicFormFieldServiceImpl.java
 * @createTime 2025年02月20日 11:41:00
 */
@Service
@Slf4j
public class DynamicFormFieldServiceImpl implements DynamicFormFieldService {

    @Resource
    private EhrEmployeeRpcService employeeRpcService;
    @Resource
    private ReimburseCommonService reimburseCommonService;
    @Resource
    private ExpenseService expenseService;

    // 发票动态表单配置
    @ApolloJsonValue("${invoice_type_dynamic_form_fields_map:{}}")
    private Map<String, List<DynamicFormFieldDto>> invoiceTypeDynamicFormFieldMap;
    @ApolloJsonValue("${invoice_type_dynamic_form_fields_map_2:{}}")
    private Map<String, List<DynamicFormFieldDto>> invoiceTypeDynamicFormFieldMapSecond;

    // 费用动态表单配置
    @ApolloJsonValue("${expense_subject_dynamic_form_fields_map:{}}")
    private Map<String, List<DynamicFormFieldDto>> expenseSubjectDynamicFormFieldsMap;
    @ApolloJsonValue("${expense_subject_dynamic_form_fields_map_2:{}}")
    private Map<String, List<DynamicFormFieldDto>> expenseSubjectDynamicFormFieldsMapSecond;

    // 费用-差旅
    @ApolloJsonValue("${expense_subject_dynamic_form_fields_cl_map_1:{}}")
    private Map<String, List<DynamicFormFieldDto>> expenseSubjectDynamicFormFieldsMapCL;
    @ApolloJsonValue("${expense_subject_dynamic_form_fields_cl_map_2:{}}")
    private Map<String, List<DynamicFormFieldDto>> expenseSubjectDynamicFormFieldsMapCLSecond;

    // 费用-异地夫妻报销单
    @ApolloJsonValue("${expense_subject_dynamic_form_fields_ydfqbxd_map:{}}")
    private Map<String, List<DynamicFormFieldDto>> expenseSubjectDynamicFormFieldsYdfqbxdMap;

    // 费用-异地夫妻报销单
    @ApolloJsonValue("${expense_subject_dynamic_form_fields_tjbxd_map:{}}")
    private Map<String, List<DynamicFormFieldDto>> expenseSubjectDynamicFormFieldsTjbxdMap;

    /**
     * 获得发票的动态字段定义
     *
     * @param invoiceType 发票类型
     * @return 获得对应发票类型的动态表单
     */
    @Override
    public List<DynamicFormFieldDto> getInvoiceDynamicFormFieldList(String invoiceType) {
        Map<String, List<DynamicFormFieldDto>> mergedMap = new HashMap<>();
        mergedMap.putAll(invoiceTypeDynamicFormFieldMap);
        mergedMap.putAll(invoiceTypeDynamicFormFieldMapSecond);

        if (mergedMap.containsKey(invoiceType)) {
            List<DynamicFormFieldDto> dynamicFormFieldDtoList = mergedMap.get(invoiceType);
            return initDefaultValues(dynamicFormFieldDtoList);
        }
        return Collections.emptyList();
    }

    /**
     * 获得费用的动态字段定义
     *
     * @param subject 费用类型
     * @return 获得对应发票类型的动态表单
     */
    public List<DynamicFormFieldDto> getExpenseDynamicFormFieldList(String subject) {
        Map<String, List<DynamicFormFieldDto>> mergedMap = new HashMap<>();
        // 一般费用
        mergedMap.putAll(expenseSubjectDynamicFormFieldsMap);
        mergedMap.putAll(expenseSubjectDynamicFormFieldsMapSecond);

        // 差旅
        mergedMap.putAll(expenseSubjectDynamicFormFieldsMapCL);
        mergedMap.putAll(expenseSubjectDynamicFormFieldsMapCLSecond);

        // 异地夫妻报销单
        mergedMap.putAll(expenseSubjectDynamicFormFieldsYdfqbxdMap);

        // 团建费用报销单
        mergedMap.putAll(expenseSubjectDynamicFormFieldsTjbxdMap);

        List<DynamicFormFieldDto> result = new ArrayList<>();
        if (mergedMap.containsKey(subject)) {
            result = mergedMap.get(subject);
        } else {
            result = mergedMap.get("default");
        }
        return initDefaultValues(result);
    }

    private List<DynamicFormFieldDto> initDefaultValues(List<DynamicFormFieldDto> dynamicFormFieldDtoList) {
        List<DynamicFormFieldDto> result = new ArrayList<>();
        List<String> listTypeCodes = Arrays.asList(CommonConstant.EXPENSE_FIELD_ID_CODE_COMPANY_MEMBERS);
        for (DynamicFormFieldDto dynamicFormFieldDto : dynamicFormFieldDtoList) {
            // List类型
            DynamicFormFieldDto newDynamicFormFieldDto = new DynamicFormFieldDto(dynamicFormFieldDto);
            if (listTypeCodes.contains(dynamicFormFieldDto.getFieldCode())) {
                newDynamicFormFieldDto.setDefaultValue(new ArrayList<>());
            } else {
                newDynamicFormFieldDto.setDefaultValue(new Object());
            }
            newDynamicFormFieldDto.setHasDefault(false);
            newDynamicFormFieldDto.setDefaultValueMap(new HashMap<>());
            result.add(newDynamicFormFieldDto);
        }
        return result;
    }

    /**
     * 校验所有必填项是否都设置了默认值
     *
     * @param list 动态表单字段列表
     * @return 如果所有必填项都设置了默认值，返回 true；否则返回 false
     */
    public boolean validateRequiredFields(List<DynamicFormFieldDto> list) {
        for (DynamicFormFieldDto field : list) {
            if (field.isMust() && isValueEmpty(field.getDefaultValue())) {
                return false;
            }
            if ("tripRouteCity".equals(field.getFieldCode())) {
                List<TripRouteCityDto> TripRouteCityList = JSONArray.parseArray(field.getDefaultValue().toString(), TripRouteCityDto.class);
                for (TripRouteCityDto tripRouteCityDto : TripRouteCityList) {
                    if (StringUtils.isEmpty(tripRouteCityDto.getStartCity()) || StringUtils.isEmpty(tripRouteCityDto.getEndCity()) || StringUtils.isEmpty(tripRouteCityDto.getSeatType())) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * RPC使用
     *
     * @param dto 请求dto
     * @return 动态表单
     */
    public List<DynamicFormFieldDto> queryDynamicFormFieldList(DynamicFormFieldListQueryDto dto) {
        List<DynamicFormFieldDto> dynamicFormFieldList;
        String invoiceType = dto.getInvoiceType();
        String formType = dto.getReimburseType();
        String firstSubject = dto.getFirstSubject();
        String secondSubject = dto.getSecondSubject();
        log.info("dto = {}", JSONObject.toJSONString(dto));

        if ("INVOICE".equals(dto.getDynamicFormType())) {
            dynamicFormFieldList = getInvoiceDynamicFormFieldList(invoiceType);
        } else {
            dynamicFormFieldList = expenseService.getExpenseDynamicFormFields(formType, firstSubject, secondSubject);
        }
        return dynamicFormFieldList;
    }

    @Override
    public List<DynamicFormFieldDto> getDynamicFormFields(OcrInvoiceDataResponse ocrData) {
        List<DynamicFormFieldDto> list = getInvoiceDynamicFormFieldList(ocrData.getInvoiceType());
        return Collections.emptyList();
    }


    @Override
    public List<DynamicFormFieldDto> getDynamicFormFields(ImageInvoicesRecogcollectMediaInvoice ocrData, InvoiceTypeEnum ticketType, String ocrParseInvoiceType) {
        List<DynamicFormFieldDto> result = new ArrayList<>();

        // 发票模板
        List<DynamicFormFieldDto> list = getInvoiceDynamicFormFieldList(ticketType.getCode());

        if (ocrData == null) {
            return list;
        }

        for (DynamicFormFieldDto fieldDto : list) {

            // 发票号码
            if (CommonConstant.INVOICE_FIELD_ID_CODE_INVOICE_NO.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getInvoiceNo())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getInvoiceNo());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            }

            // 发票号码
            else if (CommonConstant.INVOICE_FIELD_ID_CODE_INVOICE_CODE.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getInvoiceCode())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getInvoiceCode());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            }

            // 发票日期
            else if (CommonConstant.INVOICE_FIELD_ID_CODE_INVOICE_DATE.equals(fieldDto.getFieldCode())) {
                if (OcrBatchInvoiceTypeEnum.DIGITAL_RAIL_TICKET.getCode().equals(ocrParseInvoiceType)) {
                    DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                    dynamicFormFieldDto.setDefaultValue(ocrData.getTime());
                    dynamicFormFieldDto.setHasDefault(true);
                    result.add(dynamicFormFieldDto);
                } else if (OcrBatchInvoiceTypeEnum.DIGITAL_AIR_ITINERARY.getCode().equals(ocrParseInvoiceType)) {
                    DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                    dynamicFormFieldDto.setDefaultValue(ocrData.getDigitalAirItineraryInvoiceDate());
                    dynamicFormFieldDto.setHasDefault(true);
                    result.add(dynamicFormFieldDto);
                } else if (!StringUtils.isEmpty(ocrData.getInvoiceDate())) {
                    DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                    dynamicFormFieldDto.setDefaultValue(ocrData.getInvoiceDate());
                    dynamicFormFieldDto.setHasDefault(true);
                    result.add(dynamicFormFieldDto);
                }
            }

            // 发票税额
            else if (CommonConstant.INVOICE_FIELD_ID_CODE_TOTAL_TAX.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getAmountTax())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue((BigDecimal) ocrData.getTotalTax());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            }

            // 总金额
            else if (CommonConstant.INVOICE_FIELD_ID_CODE_TOTAL_AMOUNT.equals(fieldDto.getFieldCode())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                // 设置通用总金额
                dynamicFormFieldDto.setDefaultValue((BigDecimal) ocrData.getTotalAmount());
                dynamicFormFieldDto.setHasDefault(true);

                List<String> amountTaxCodeList = OcrBatchInvoiceTypeEnum.needResetPurchaserTaxNoCodeList();
                // 飞机票、火车票 取amountTax
                if (amountTaxCodeList.contains(ocrParseInvoiceType)) {
                    dynamicFormFieldDto.setDefaultValue((BigDecimal) ocrData.getAmountTax());
                }
                result.add(dynamicFormFieldDto);
            }

            // 购方名称
            else if (CommonConstant.INVOICE_FIELD_ID_CODE_PURCHASER_NAME.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getPurchaserName())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue((ocrData.getPurchaserName()));
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            }

            // 购方税号
            else if (CommonConstant.INVOICE_FIELD_ID_CODE_PURCHASER_TAX_NO.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getPurchaserTaxNo())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getPurchaserTaxNo());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            }

            // 销方名称
            else if (CommonConstant.INVOICE_FIELD_ID_CODE_SALE_NAME.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getSaleName())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getSaleName());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            }

            // 销方税号
            else if (CommonConstant.INVOICE_FIELD_ID_CODE_SALE_TAX_NO.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getSaleTaxNo())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getSaleTaxNo());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            } else if (CommonConstant.INVOICE_FIELD_ID_CODE_LEAVE_TIME.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getLeaveTime())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getLeaveTime());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            } else if (CommonConstant.INVOICE_FIELD_ID_CODE_ARRIVE_TIME.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getArriveTime())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getArriveTime());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            } else if (CommonConstant.INVOICE_FIELD_ID_CODE_LEAVE_PLACE.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getLeaveCity())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getLeaveCity());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            } else if (CommonConstant.INVOICE_FIELD_ID_CODE_ARRIVE_PLACE.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getArriveCity())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getArriveCity());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            } else if (CommonConstant.INVOICE_FIELD_ID_CODE_MILEAGE.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getMileage())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getMileage());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            }


            // 通行费时间 = 开票时间
            else if (CommonConstant.INVOICE_FIELD_ID_CODE_TIME.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getInvoiceDate())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getInvoiceDate());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            } else if (CommonConstant.INVOICE_FIELD_ID_CODE_EXIT.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getRoadExit())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getRoadExit());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            } else if (CommonConstant.INVOICE_FIELD_ID_CODE_ENTRANCE.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getEntrance())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getEntrance());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            } else if (CommonConstant.INVOICE_FIELD_ID_CODE_DRAWER.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getDrawer())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getDrawer());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            } else if (CommonConstant.INVOICE_FIELD_ID_CODE_TRAIN_SEAT.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getTrainSeat())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getTrainSeat());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            } else if (CommonConstant.INVOICE_FIELD_ID_CODE_TRAIN_NUMBER.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getTrainNumber())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getTrainNumber());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            }

            //  飞机行程单
            else if (CommonConstant.INVOICE_FIELD_ID_CODE_FROM_STATION.equals(fieldDto.getFieldCode()) && !CollectionUtils.isEmpty(ocrData.getInvoiceTravelList()) && !StringUtils.isEmpty(ocrData.getInvoiceTravelList().get(0).getFromStation())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getInvoiceTravelList().get(0).getFromStation());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            } else if (CommonConstant.INVOICE_FIELD_ID_CODE_TO_STATION.equals(fieldDto.getFieldCode()) && !CollectionUtils.isEmpty(ocrData.getInvoiceTravelList()) && !StringUtils.isEmpty(ocrData.getInvoiceTravelList().get(0).getToStation())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getInvoiceTravelList().get(0).getToStation());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            } else if (CommonConstant.INVOICE_FIELD_ID_CODE_SEAT_LEVEL.equals(fieldDto.getFieldCode()) && !CollectionUtils.isEmpty(ocrData.getInvoiceTravelList()) && !StringUtils.isEmpty(ocrData.getInvoiceTravelList().get(0).getSeatLevel())) {
                String ocrSeatLevel = ocrData.getInvoiceTravelList().get(0).getSeatLevel();

                List<String> validSeatType = PlaneCabinTypeEnum.getPlaneCabinList()
                        .stream().map(LabelValueResponse::getLabel)
                        .collect(Collectors.toList());

                if (validSeatType.contains(ocrSeatLevel)) {
                    DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                    dynamicFormFieldDto.setDefaultValue(ocrSeatLevel);
                    dynamicFormFieldDto.setHasDefault(true);
                    result.add(dynamicFormFieldDto);
                }
            } else if (CommonConstant.INVOICE_FIELD_ID_CODE_FLIGHT_NO.equals(fieldDto.getFieldCode()) && !CollectionUtils.isEmpty(ocrData.getInvoiceTravelList()) && !StringUtils.isEmpty(ocrData.getInvoiceTravelList().get(0).getFlightNo())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getInvoiceTravelList().get(0).getFlightNo());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            } else if (CommonConstant.INVOICE_FIELD_ID_CODE_ID_NUM.equals(fieldDto.getFieldCode()) && !StringUtils.isEmpty(ocrData.getIdNum())) {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(ocrData.getIdNum());
                dynamicFormFieldDto.setHasDefault(true);
                result.add(dynamicFormFieldDto);
            } else {
                DynamicFormFieldDto dynamicFormFieldDto = new DynamicFormFieldDto(fieldDto);
                dynamicFormFieldDto.setDefaultValue(new Object());
                dynamicFormFieldDto.setHasDefault(false);
                result.add(dynamicFormFieldDto);
            }
        }
        return result;
    }

    @Override
    public void validateAndCorrectHasDefault(List<DynamicFormFieldDto> list) {
        for (DynamicFormFieldDto fieldDto : list) {

            Object defaultValue = fieldDto.getDefaultValue();
            boolean hasDefault = fieldDto.isHasDefault();

            if (isValueEmpty(defaultValue) && hasDefault) {
                fieldDto.setHasDefault(false);
            } else if (!isValueEmpty(defaultValue) && !hasDefault) {
                fieldDto.setHasDefault(true);
            }

            // 金额转换
            if (CommonConstant.DYNAMIC_FORM_COMPONENT_MONEY.equals(fieldDto.getComponent())) {
                if (!isValueEmpty(defaultValue)) {
                    try {
                        BigDecimal decimalValue = null;

                        if (defaultValue instanceof BigDecimal) {
                            decimalValue = (BigDecimal) defaultValue;
                        } else if (defaultValue instanceof Number) {
                            decimalValue = BigDecimal.valueOf(((Number) defaultValue).doubleValue());
                        } else if (defaultValue instanceof String) {
                            decimalValue = new BigDecimal((String) defaultValue);
                        }

                        if (decimalValue != null) {
                            decimalValue = decimalValue.setScale(2, RoundingMode.HALF_UP);
                            fieldDto.setDefaultValue(decimalValue.toPlainString());
                        }
                    } catch (Exception e) {
                        log.warn("转换 defaultValue={} 为 BigDecimal 出错", defaultValue, e);
                    }
                }
            }

        }
    }

    public Object getDefaultValueObject(List<DynamicFormFieldDto> list, String fieldCode) {
        for (DynamicFormFieldDto fieldDto : list) {
            if (fieldDto.getFieldCode().equals(fieldCode)) {
                return fieldDto.getDefaultValue();
            }
        }
        return null;
    }

    @Override
    public void buildDynamicFormDefaultValueMap(List<DynamicFormFieldDto> list) {
        for (DynamicFormFieldDto fieldDto : list) {
            Map<String, Object> result = new HashMap<>();

            // 内部员工映射
            if (CommonConstant.EXPENSE_FIELD_ID_CODE_COMPANY_MEMBERS.equals(fieldDto.getFieldCode())) {
                Object defaultValue = fieldDto.getDefaultValue();
                if (!isValueEmpty(defaultValue)) {
                    List<String> userIdList = (List<String>) defaultValue;
                    List<EmployeeEntity> employeeEntities = employeeRpcService.batchQueryEmployee(userIdList, true);
                    Map<String, String> userIdNameMap = StreamUtil.toMap(employeeEntities, EmployeeEntity::getUserId, EmployeeEntity::getUserName);
                    for (String userId : userIdList) {
                        if (userIdNameMap.containsKey(userId)) {
                            result.put(userId, userIdNameMap.get(userId));
                        } else {
                            result.put(userId, userId);
                        }
                    }
                }
            }

            fieldDto.setDefaultValueMap(result);
        }
    }

    @Override
    public void buildDynamicFormDefaultValue(String secondSubject, List<DynamicFormFieldDto> list) {

        if (!StringUtils.isEmpty(secondSubject) && Arrays.asList(ExpenseSecondSubjectEnum.YWZDCY.getSubjectCode(), ExpenseSecondSubjectEnum.YWZDQT.getSubjectCode()).contains(secondSubject)) {
            String userId = reimburseCommonService.getUserId();

            //  日常费用
            if (!StringUtils.isEmpty(userId)) {
                list.forEach(dynamicFormFieldDto -> {
                    //  公司成员默认值
                    if (dynamicFormFieldDto.getFieldCode().equals(CommonConstant.EXPENSE_FIELD_ID_CODE_COMPANY_MEMBERS)) {
                        List<String> userIdList = new ArrayList<>();
                        userIdList.add(userId);
                        dynamicFormFieldDto.setDefaultValue(userIdList);
                        dynamicFormFieldDto.setHasDefault(true);
                    }
                    // base 地
                    if (dynamicFormFieldDto.getFieldCode().equals(CommonConstant.EXPENSE_FIELD_ID_CODE_CITY)) {
                        EmployeeEntity employeeEntity = employeeRpcService.queryEhrEmployeeEntity(userId, true);
                        String workplace = employeeEntity.getWorkplace();
                        dynamicFormFieldDto.setDefaultValue(workplace);
                        dynamicFormFieldDto.setHasDefault(true);
                    }
                });
            }
        }
    }

    @Override
    public boolean isValueEmpty(Object value) {
        if (value == null) {
            return true;
        }

        if (value instanceof String) {
            return StringUtils.isEmpty((String) value) || "{}".equals((String) value);
        } else if (value instanceof Collection) { // 适用于 List、Set、JSONArray
            return ((Collection<?>) value).isEmpty();
        } else if (value instanceof Map) { // 适用于 JSONObject 和 fastjson.JSONObject
            return ((Map<?, ?>) value).isEmpty();
        } else if (value.getClass() == Object.class) { // 判断 new Object() 的情况
            return true;
        }

        return false;
    }

    @Override
    public boolean isAmountNotValid(String amount) {
        try {
            new BigDecimal(amount);
            return false;
        } catch (Exception e) {
            return true;
        }
    }

    @Override
    public void updateDynamicFromDefaultValue(List<DynamicFormFieldDto> list, String filedCode, Object value) {
        for (DynamicFormFieldDto dynamicFormFieldDto : list) {
            if (!StringUtils.isEmpty(dynamicFormFieldDto.getFieldCode()) && dynamicFormFieldDto.getFieldCode().equals(filedCode)) {
                dynamicFormFieldDto.setDefaultValue(value);
            }
        }
    }
}
