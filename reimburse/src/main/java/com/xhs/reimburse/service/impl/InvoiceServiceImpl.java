package com.xhs.reimburse.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.xhs.finance.sso.UserInfoBag;
import com.xhs.oa.office.rpc.CompanyRpcService;
import com.xhs.oa.office.threadPool.ThreadPoolManager;
import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.reimburse.assembler.InvoiceAssembler;
import com.xhs.reimburse.constant.CommonConstant;
import com.xhs.reimburse.constant.ErrorConstant;
import com.xhs.reimburse.enums.*;
import com.xhs.reimburse.enums.travel.InvoiceCheckItemEnum;
import com.xhs.reimburse.event.InvoiceAmountChangedEvent;
import com.xhs.reimburse.event.InvoiceLogicDeleteEvent;
import com.xhs.reimburse.mapper.InvoiceMapper;
import com.xhs.reimburse.modal.dto.*;
import com.xhs.reimburse.modal.dto.CheckResultDetailDto;
import com.xhs.reimburse.modal.entity.InvoiceEntity;
import com.xhs.reimburse.modal.request.BatchOcrParseRequest;
import com.xhs.reimburse.modal.request.BatchSaveInvoiceRequest;
import com.xhs.reimburse.modal.request.PageQueryInvoiceRequest;
import com.xhs.reimburse.modal.response.BatchOcrParseResponse;
import com.xhs.reimburse.modal.response.LabelValueResponse;
import com.xhs.reimburse.rpc.consumer.MultiCdnRpcService;
import com.xhs.reimburse.rpc.consumer.ocr.OcrInvoiceDataResponse;
import com.xhs.reimburse.rpc.consumer.ocr.OcrRpcService;
import com.xhs.reimburse.rpc.consumer.ocr.bean.ImageInvoicesRecogcollectMediaInvoice;
import com.xhs.reimburse.rpc.consumer.ocr.bean.ImageInvoicesRecogcollectResponse;
import com.xhs.reimburse.service.DynamicFormFieldService;
import com.xhs.reimburse.service.InvoiceService;
import com.xhs.reimburse.service.ReimburseCommonService;
import com.xhs.reimburse.service.external.company.OaCompanyRpcService;
import com.xhs.reimburse.utils.DateUtil;
import com.xiaohongshu.erp.common.exception.BusinessException;
import com.xiaohongshu.erp.common.framework.page.PageResult;
import com.xiaohongshu.fls.rpc.oacommon.company.response.CompanyInfoResDTO;
import com.xiaohongshu.fls.rpc.oacommon.company.response.ExpenseInfo;
import com.xiaohongshu.fls.rpc.oacontract.baiwang.response.InterfaceResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date :2025/02/12 - 下午2:44
 * @description :
 */
@Slf4j
@Service
public class InvoiceServiceImpl extends ServiceImpl<InvoiceMapper, InvoiceEntity> implements InvoiceService {
    // mapper
    @Resource
    private InvoiceMapper invoiceMapper;
    // service
    @Resource
    private ReimburseCommonService reimburseCommonService;
    @Resource
    private DynamicFormFieldService dynamicFormFieldService;
    @Resource
    private MultiCdnRpcService multiCdnRpcService;
    @Resource
    private OcrRpcService ocrRpcService;
    @Resource
    private CompanyRpcService companyRpcService;
    @Resource
    private OaCompanyRpcService oaCompanyRpcService;
    @Resource
    private InvoiceAssembler invoiceAssembler;
    @Resource
    private RelationExpenseInvoiceServiceImpl relationExpenseInvoiceServiceImpl;
    @Resource
    private ApplicationEventPublisher eventPublisher;

    // 常量
    private final String FIELD_CODE = "fieldCode";
    private final String FIELD_CODE_TOTAL_AMOUNT = "totalAmount";
    private final String FIELD_CODE_INVOICE_DATE = "invoiceDate";
    private final String DEFAULT_VALUE = "defaultValue";
    private final Integer MAX_RETRIES = 2;

    @Override
    public Integer getPendingCount() {
        String userId = reimburseCommonService.getUserId();
        LambdaQueryWrapper<InvoiceEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(InvoiceEntity::getStatus, RecordStatusEnum.VALID.getCode());
        queryWrapper.eq(InvoiceEntity::getCreatorUserId, userId);
        queryWrapper.in(InvoiceEntity::getInvoiceStatus, Arrays.asList(InvoiceStatusEnum.DBC.getCode(), InvoiceStatusEnum.YBC.getCode()));
        queryWrapper.eq(InvoiceEntity::getInvoiceExpenseStatus, InvoiceExpenseStatusEnum.WGL.getCode());
        queryWrapper.eq(InvoiceEntity::getInvoiceFormStatus, InvoiceFormStatusEnum.WGL.getCode());
        return invoiceMapper.selectCount(queryWrapper);
    }

    @Override
    public void logicDeleteInvoice(String uuId) {
        // 发票删除-仅本人可操作
        String loginUserId = reimburseCommonService.getUserId();
        InvoiceEntity invoiceEntity = updateInvoiceCheck(uuId, loginUserId);
        invoiceEntity.setStatus(RecordStatusEnum.INVALID.getCode());
        invoiceMapper.updateById(invoiceEntity);
        // 发送发票删除事件
        eventPublisher.publishEvent(new InvoiceLogicDeleteEvent(uuId));
    }

    public InvoiceEntity updateInvoiceCheck(String invoiceUuid, String loginUserId) {
        AssertHelper.notBlank(invoiceUuid, "发票编号不能为空");
        InvoiceEntity invoiceEntity = getInvoiceEntityByUuid(invoiceUuid, false);
        AssertHelper.notNull(invoiceEntity, "发票不存在");
        if (!loginUserId.equals(invoiceEntity.getCreatorUserId())) {
            throw new BusinessException(ErrorConstant.NOT_AUTH_CODE, ErrorConstant.NOT_AUTH_MSG);
        }
        Integer invoiceFormStatus = invoiceEntity.getInvoiceFormStatus();
        List<Integer> errorCodeList = Lists.newArrayList(InvoiceFormStatusEnum.SHZ.getCode(), InvoiceFormStatusEnum.YBX.getCode());
        if (errorCodeList.contains(invoiceFormStatus)) {
            throw new BusinessException(999, "发票关联报销单状态不可修改");
        }
        return invoiceEntity;
    }

    @Override
    public List<DynamicFormFieldDto> getInvoiceDynamicFormFields(String invoiceType) {
        List<DynamicFormFieldDto> invoiceDynamicFormFieldList = dynamicFormFieldService.getInvoiceDynamicFormFieldList(invoiceType);
        dynamicFormFieldService.validateAndCorrectHasDefault(invoiceDynamicFormFieldList);
        return invoiceDynamicFormFieldList;
    }

    @Override
    public List<LabelValueResponse> getInvoiceTypes() {
        List<LabelValueResponse> responseList = new ArrayList<>();
        for (InvoiceTypeEnum type : InvoiceTypeEnum.values()) {
            LabelValueResponse response = new LabelValueResponse();
            response.setLabel(type.getName());
            response.setValue(type.getCode());
            responseList.add(response);
        }
        return responseList;
    }

    @Override
    public PageResult<InvoiceDto> pageQueryInvoice(PageQueryInvoiceRequest request) {
        String userId = reimburseCommonService.getUserId();
        Page<InvoiceEntity> page = new Page<>(request.getPageNum(), request.getPageSize());

        LambdaQueryWrapper<InvoiceEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(InvoiceEntity::getStatus, RecordStatusEnum.VALID.getCode());
        queryWrapper.eq(InvoiceEntity::getCreatorUserId, userId);

        if (request.getInvoiceStatus() == 1) {
            queryWrapper.eq(InvoiceEntity::getInvoiceExpenseStatus, InvoiceExpenseStatusEnum.WGL.getCode());
            queryWrapper.eq(InvoiceEntity::getInvoiceFormStatus, InvoiceFormStatusEnum.WGL.getCode());
        }

        if (request.getInvoiceStatus() == 2) {
            queryWrapper.in(InvoiceEntity::getInvoiceStatus, Arrays.asList(InvoiceStatusEnum.YBC.getCode()));
            queryWrapper.eq(InvoiceEntity::getInvoiceExpenseStatus, InvoiceExpenseStatusEnum.YGL.getCode());
        }

        if (request.getInvoiceStatus() == 3) {
            // 已补充 & 未关联的
            queryWrapper.in(InvoiceEntity::getInvoiceStatus, Arrays.asList(InvoiceStatusEnum.YBC.getCode()));
            queryWrapper.eq(InvoiceEntity::getInvoiceExpenseStatus, InvoiceExpenseStatusEnum.WGL.getCode());
            if (request.getFilterByInvoiceValidate()) {
                queryWrapper.in(InvoiceEntity::getInvoiceValidateResult, Arrays.asList(InvoiceValidateEnum.NO_NEED_VALIDATE.getCode(), InvoiceValidateEnum.VALIDATE_SUCCESS.getCode()));
            }
        }

        queryWrapper.orderByDesc(InvoiceEntity::getId);
        Page<InvoiceEntity> invoiceEntityPage = invoiceMapper.selectPage(page, queryWrapper);

        PageResult<InvoiceDto> pageResult = new PageResult<>();
        List<InvoiceDto> invoiceDtoList = invoiceAssembler.toDtoList(invoiceEntityPage.getRecords());

        // 发票创建人不能看到发票是否修改
        invoiceDtoList.stream().forEach(invoiceDto -> {
            if (OcrContentModifiedEnum.MODIFIED.getCode() == invoiceDto.getOcrContentModified() && invoiceDto.getCreatorUserId().equals(reimburseCommonService.getUserId())) {
                invoiceDto.setOcrContentModified(OcrContentModifiedEnum.NOT_MODIFIED.getCode());
            }
        });

        pageResult.setList(invoiceDtoList);
        pageResult.setTotal((int) invoiceEntityPage.getTotal());
        pageResult.setTotalPage((int) invoiceEntityPage.getPages());
        return pageResult;
    }

    @Override
    public BatchOcrParseResponse batchOcrParse(BatchOcrParseRequest request, boolean fileCheck) {
        log.info("InvoiceServiceImpl batchOcrParse. request:{},fileCheck:{}", request, fileCheck);
        BatchOcrParseResponse result = new BatchOcrParseResponse();
        List<InvoiceDto> ocrParseSuccessList = new ArrayList<>();
        List<InvoiceDto> ocrParseFailureList = new ArrayList<>();
        List<InvoiceDto> ocrParseDuplicateList = new ArrayList<>();

        // 设置文件URL
        List<FileInfoDto> fileInfoList = request.getOrcParseItems();

        // 填充文件字段
        if (fileCheck) {
            multiCdnRpcService.batchGetAndSetFileUrl(fileInfoList);
        }
        log.info("InvoiceServiceImpl batchOcrParse real fileInfoList. fileInfoList:{},fileCheck:{}", fileInfoList, fileCheck);

        // 线程池执行OCR解析
        List<CompletableFuture<BatchOcrParseResponse>> futureList = fileInfoList.stream()
                .map(file -> CompletableFuture.supplyAsync(() -> ocrBatchParseInvoice(file), ThreadPoolManager.commonExecutorService))
                .collect(Collectors.toList());

        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();

        futureList.forEach(future -> {
            try {
                BatchOcrParseResponse ocrResult = future.get();
                ocrParseSuccessList.addAll(ocrResult.getOcrParseSuccessList());
                ocrParseFailureList.addAll(ocrResult.getOcrParseFailureList());
                ocrParseDuplicateList.addAll(ocrResult.getOcrParseDuplicateList());
            } catch (InterruptedException | ExecutionException e) {
                log.error("batchOcrParse error", e);
                e.printStackTrace();
            }
        });

        // 识别成功的发票，去重复
        deduplicateInvoices(ocrParseSuccessList, ocrParseDuplicateList);

        result.setOcrParseSuccessList(ocrParseSuccessList);
        result.setOcrParseFailureList(ocrParseFailureList);
        result.setOcrParseDuplicateList(ocrParseDuplicateList);
        return result;
    }


    public void deduplicateInvoices(List<InvoiceDto> ocrParseSuccessList, List<InvoiceDto> ocrParseDuplicateList) {
        Set<String> seen = new HashSet<>();
        Iterator<InvoiceDto> iterator = ocrParseSuccessList.iterator();

        while (iterator.hasNext()) {
            InvoiceDto invoice = iterator.next();

            String key = String.join("-",
                    Objects.toString(invoice.getInvoiceNo(), ""),
                    Objects.toString(invoice.getInvoiceCode(), ""),
                    Objects.toString(invoice.getInvoiceType(), "")
            );

            if (!seen.add(key)) {
                ocrParseDuplicateList.add(invoice);
                iterator.remove();
            }
        }
    }

    @Override
    public BatchOcrParseResponse ocrBatchParseInvoice(FileInfoDto fileInfoDto) {
        BatchOcrParseResponse result = new BatchOcrParseResponse();
        String fileUrl = fileInfoDto.getUrl();

        try {

            List<ImageInvoicesRecogcollectMediaInvoice> parseInvoiceList = ocrBatchParseInvoiceWithRetry(fileUrl, MAX_RETRIES);
            log.info("ocrBatchParseInvoice parseInvoiceList data. fileUrl:{},parseInvoiceList:{}", fileUrl, parseInvoiceList);

            List<InvoiceDto> successList = new ArrayList<>();
            List<InvoiceDto> duplicateList = new ArrayList<>();

            for (ImageInvoicesRecogcollectMediaInvoice invoiceParseResult : parseInvoiceList) {
                InvoiceDto invoiceDto = new InvoiceDto();
                invoiceDto.setInvoiceType(invoiceParseResult.getInvoiceType());
                invoiceDto.setInvoiceCode(invoiceParseResult.getInvoiceCode());
                invoiceDto.setInvoiceNo(invoiceParseResult.getInvoiceNo());
                invoiceDto.setCheckCode(invoiceParseResult.getCheckCode());

                // 设置ocr内容
                invoiceDto.setOcrSourceContent(JSON.toJSONString(invoiceParseResult));

                // 动态表单
                InvoiceTypeEnum invoiceTypeEnum = OcrBatchInvoiceTypeEnum.getInvoiceTypeByCode(invoiceParseResult.getInvoiceType());
                if (invoiceTypeEnum == null) {
                    invoiceTypeEnum = InvoiceTypeEnum.OTHER;
                }
                List<DynamicFormFieldDto> dynamicFormFields = dynamicFormFieldService.getDynamicFormFields(invoiceParseResult, invoiceTypeEnum, invoiceParseResult.getInvoiceType());

                invoiceDto.setDynamicFormFieldDtoList(dynamicFormFields);

                // 设置文件
                invoiceDto.setUploadFileInfo(fileInfoDto);
                invoiceDto.setTicketType(invoiceTypeEnum.getCode());

                List<InvoiceEntity> invoiceEntities = queryInvoiceList(invoiceDto.getInvoiceNo(), invoiceDto.getInvoiceType(), invoiceDto.getInvoiceCode());
                if (CollectionUtils.isEmpty(invoiceEntities)) {
                    successList.add(invoiceDto);
                } else {
                    String userId = invoiceEntities.get(0).getCreatorUserId();
                    String uuid = invoiceEntities.get(0).getUuid();
                    invoiceDto.setUuid(uuid);
                    invoiceDto.setCreatorUserId(userId);
                    duplicateList.add(invoiceDto);
                }
            }

            result.setOcrParseFailureList(Collections.emptyList());
            result.setOcrParseSuccessList(successList);
            result.setOcrParseDuplicateList(duplicateList);
            return result;
        } catch (BusinessException e) {
            // 解析失败
            InvoiceDto invoiceDto = new InvoiceDto();
            invoiceDto.setUploadFileInfo(fileInfoDto);

            result.setOcrParseFailureList(Collections.singletonList(invoiceDto));
            result.setOcrParseSuccessList(Collections.emptyList());
            result.setOcrParseDuplicateList(Collections.emptyList());
        }
        return result;
    }


    public List<ImageInvoicesRecogcollectMediaInvoice> ocrBatchParseInvoiceWithRetry(String fileUrl, int maxRetries) {
        int attempt = 0;
        while (attempt < maxRetries) {
            attempt++;

            try {
                // 调用 OCR 解析服务
                InterfaceResponse response = ocrRpcService.batchOcrParse(fileUrl);

                // 解析 JSON 响应
                ImageInvoicesRecogcollectResponse ocrResponse = JSONObject.parseObject(
                        response.getData(), ImageInvoicesRecogcollectResponse.class
                );
                log.info("ocrBatchParseInvoiceWithRetry success. attempt:{} url:{}, ocrResponse:{}", attempt, fileUrl, ocrResponse);

                // 校验 OCR 响应
                if (ocrResponse == null || !ocrResponse.getSuccess() || CollectionUtils.isEmpty(ocrResponse.getResponse())) {
                    log.warn("OCR 解析失败，尝试重试... attempt:{}", attempt);
                    continue; // 直接重试
                }

                List<ImageInvoicesRecogcollectMediaInvoice> parseInvoiceList = ocrResponse.getResponse().get(0).getMediaInvoiceList();

                // 校验 `parseInvoiceList`
                if (CollectionUtils.isEmpty(parseInvoiceList)) {
                    log.warn("OCR 解析返回空列表，尝试重试... attempt:{}", attempt);
                    continue; // 直接重试
                }

                // 校验 `InspectionStatus`
                boolean hasError = false;
                for (ImageInvoicesRecogcollectMediaInvoice item : parseInvoiceList) {
                    //  异常状态
                    //  1、 item.getInspectionStatus() 未设置
                    //  2、 item.getInspectionStatus() ！= 1 (查询成功)
                    if (StringUtils.isNotBlank(item.getInspectionErrorDesc()) && !CommonConstant.OCR_INSPECTION_STATUS_SUCCESS.equals(item.getInspectionStatus())) {
                        log.warn("OCR 解析异常，inspectionErrorDesc:{}，尝试重试... attempt:{}", item, attempt);
                        hasError = true;
                        break;
                    }
                }

                if (hasError) {
                    continue; // 直接重试
                }

                // 解析成功，返回结果
                return parseInvoiceList;
            } catch (Exception e) {
                log.error("OCR 解析异常，尝试重试... attempt:{} url:{} error:{}", attempt, fileUrl, e.getMessage(), e);
            }

            // 适当的重试间隔，避免频繁请求
            try {
                Thread.sleep(2500L);
            } catch (InterruptedException ignored) {
                Thread.currentThread().interrupt();
            }
        }

        // 最终失败，抛出异常
        throw new BusinessException("OCR 解析失败，达到最大重试次数：" + maxRetries);
    }

    public InvoiceDto convertOcrInvoiceDataToDto(OcrInvoiceDataResponse response, FileInfoDto fileInfoDto) {
        InvoiceDto invoiceDto = new InvoiceDto();
        String invoiceType = response.getInvoiceType();

        invoiceDto.setInvoiceType(invoiceType);
        invoiceDto.setInvoiceNo(response.getInvoiceNo());
        invoiceDto.setInvoiceCode(response.getInvoiceCode());
        invoiceDto.setUploadFileInfo(fileInfoDto);
        invoiceDto.setInvoiceTypeTag(InvoiceTypeEnum.getInvoiceTag(invoiceType));
        invoiceDto.setExceptionList(new ArrayList<>());
        // 动态表单
        invoiceDto.setDynamicFormFieldDtoList(dynamicFormFieldService.getDynamicFormFields(response));
        // 表单状态
        invoiceDto.setInvoiceStatus(getSaveInvoiceStatus(invoiceDto.getDynamicFormFieldDtoList()).getCode());
        return invoiceDto;
    }

    public InvoiceStatusEnum getSaveInvoiceStatus(List<DynamicFormFieldDto> list) {
        return dynamicFormFieldService.validateRequiredFields(list)
                ? InvoiceStatusEnum.YBC
                : InvoiceStatusEnum.DBC;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "oaOfficeTransactionManager")
    public List<String> batchSaveInvoices(BatchSaveInvoiceRequest request) {
        List<String> uuIdList = new ArrayList<>();
        String userId = StrUtil.isBlank(request.getUserId()) ? reimburseCommonService.getUserId() : request.getUserId();

        // 公司主体
        ExpenseInfo expenseInfo = oaCompanyRpcService.queryExpenseInfos(userId);

        for (InvoiceDto invoiceDto : request.getInvoiceDtoList()) {
            // 唯一性校验
            uniqueCheck(invoiceDto);

            // 设置通用值
            String uuId = reimburseCommonService.getUuid();
            Integer invoiceSource = Objects.isNull(request.getInvoiceSource()) ? InvoiceSourceEnum.BUSINESS.getCode() : request.getInvoiceSource();

            invoiceDto.setUuid(uuId);
            invoiceDto.setOcrContentModified(OcrContentModifiedEnum.NOT_MODIFIED.getCode());
            invoiceDto.setCreatorUserId(userId);
            invoiceDto.setCreateTime(new Date());
            invoiceDto.setUpdateTime(new Date());
            invoiceDto.setStatus(RecordStatusEnum.VALID.getCode());
            invoiceDto.setInvoiceStatus(getSaveInvoiceStatus(invoiceDto.getDynamicFormFieldDtoList()).getCode());
            invoiceDto.setInvoiceSource(invoiceSource);
            invoiceDto.setInvoiceExpenseStatus(InvoiceExpenseStatusEnum.WGL.getCode());
            invoiceDto.setInvoiceFormStatus(InvoiceFormStatusEnum.WGL.getCode());

            InvoiceEntity invoiceEntity = invoiceAssembler.toEntity(invoiceDto);
            invoiceMapper.insert(invoiceEntity);

            // 发票验真&异常识别
            InvoiceDto invoiceDtoByUuid = getInvoiceDtoByUuid(uuId);
            doInvoiceValidateAndExceptionCheck(invoiceDtoByUuid, expenseInfo);
            invoiceMapper.updateById(invoiceAssembler.toEntity(invoiceDtoByUuid));

            uuIdList.add(uuId);
        }
        return uuIdList;
    }


    /**
     * 发票验真和异常检查
     *
     * @param invoiceDto
     * @param expenseInfo
     */
    private void doInvoiceValidateAndExceptionCheck(InvoiceDto invoiceDto, ExpenseInfo expenseInfo) {
        invoiceDto.setExceptionList(new ArrayList<>());

        // 购方主体税号存在且用户主体TAG不一致时,主体不一致
        List<String> needCheckCompany = InvoiceTypeEnum.getNeedCheckCompany();
        if (needCheckCompany.contains(invoiceDto.getTicketType()) && expenseInfo != null && StringUtils.isNotBlank(expenseInfo.getTaxNumber())) {
            if (invoiceCompanyNameTaxNumberError(invoiceDto)) {
                InvoiceExceptionDto exceptionDto = InvoiceExceptionEnum.getInvoiceExceptionDto(InvoiceExceptionEnum.COMPANY_NAME_TAX_NUMBER_ERROR);
                exceptionDto.setExceptionDes(String.format(exceptionDto.getExceptionDes(), expenseInfo.getCompanyName(), expenseInfo.getTaxNumber()));
                invoiceDto.getExceptionList().add(exceptionDto);
            }
        }

        // 验真
        ocrRpcService.invoiceValidate(invoiceDto);
    }

    @Override
    public boolean invoiceCompanyNameTaxNumberError(InvoiceDto invoiceDto) {
        String purchaserTaxNo = invoiceDto.getPurchaserTaxNo();
        String purchaserName = invoiceDto.getPurchaserName();
        String userId = UserInfoBag.get().getUserId();
        List<String> formTypeList = Lists.newArrayList(
                ReimburseTypeEnum.YBFYBXD.getType(),
                ReimburseTypeEnum.CLBXD.getType(),
                ReimburseTypeEnum.TJBXD.getType(),
                ReimburseTypeEnum.YDFQBXD.getType()
        );

        // 查询各个单据类型所能拿到的公司列表
        List<CompanyInfoResDTO> companyInfoList = companyRpcService.queryCompanyInfoList(formTypeList, userId);
        if (CollectionUtils.isEmpty(companyInfoList)) {
            return true;
        }

        // 如果发票的税号在报销公司列表中找不到对应的公司名称，或者发票的公司名称与报销公司列表中的不一致，则主体/税号错误
        Map<String, String> reiburseCompanyInfoMap = companyInfoList.stream().distinct().collect(Collectors.toMap(CompanyInfoResDTO::getTaxNumber, CompanyInfoResDTO::getCompanyName, (a, b) -> a));
        String mapPurchaserName = reiburseCompanyInfoMap.getOrDefault(purchaserTaxNo, "NOT_FOUND");
        return !StringUtils.equals(mapPurchaserName, purchaserName);
    }

    @Override
    public InvoiceDto getInvoiceDtoByUuid(String uuid) {
        return this.getInvoiceDtoByUuid(uuid, false);
    }

    //  根据uuid获取有效发票
    @Override
    public  List<InvoiceEntity> getValidInvoiceEntities(String uuid) {
        LambdaQueryWrapper<InvoiceEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(InvoiceEntity::getUuid, uuid);
        queryWrapper.eq(InvoiceEntity::getStatus, RecordStatusEnum.VALID.getCode());
        List<InvoiceEntity> invoiceEntities = invoiceMapper.selectList(queryWrapper);
        return invoiceEntities;
    }

    @Override
    public InvoiceDto getInvoiceForBotByUuid(String uuid) {
        InvoiceDto invoiceDto = this.getInvoiceDtoByUuid(uuid, false);
        invoiceDateConvertToDotSeparatedDate(Collections.singletonList(invoiceDto));
        return invoiceDto;
    }

    @Override
    public InvoiceDto getInvoiceDtoByUuid(String uuid, boolean withDeleted) {
        InvoiceEntity invoiceEntity = getInvoiceEntityByUuid(uuid, withDeleted);
        InvoiceDto invoiceDto = invoiceAssembler.toDto(invoiceEntity);

        List<String> relationExpenseUuids = relationExpenseInvoiceServiceImpl.queryRelationExpenseUuids(invoiceDto.getUuid());
        invoiceDto.setRelationExpenseUuids(relationExpenseUuids);

        // 发票创建人不能看到发票是否修改
        if (OcrContentModifiedEnum.MODIFIED.getCode() == invoiceDto.getOcrContentModified() && invoiceDto.getCreatorUserId().equals(reimburseCommonService.getUserId())) {
            invoiceDto.setOcrContentModified(OcrContentModifiedEnum.NOT_MODIFIED.getCode());
        }

        return invoiceDto;
    }

    @Override
    public void  updateInvoice(InvoiceDto invoiceDto) {
        // 更新基础信息 仅本人可操作
        String loginUserId = reimburseCommonService.getUserId();
        InvoiceEntity invoiceEntity = updateInvoiceCheck(invoiceDto.getUuid(), loginUserId);
        InvoiceDto sourceInvoiceDto = invoiceAssembler.toDto(invoiceEntity);

        InvoiceEntity invoiceUpdate = invoiceAssembler.toEntity(invoiceDto);
        invoiceUpdate.setId(invoiceEntity.getId());
        invoiceUpdate.setUpdateTime(new Date());
        invoiceUpdate.setOcrContentModified(OcrContentModifiedEnum.MODIFIED.getCode());
        invoiceMapper.updateById(invoiceUpdate);

        // 验真&发票状态
        InvoiceDto updatedInvoiceDto = getInvoiceDtoByUuid(invoiceDto.getUuid());
        doInvoiceValidateAndExceptionCheck(updatedInvoiceDto, oaCompanyRpcService.queryExpenseInfos(reimburseCommonService.getUserId()));
        updatedInvoiceDto.setInvoiceStatus(getSaveInvoiceStatus(invoiceDto.getDynamicFormFieldDtoList()).getCode());
        updatedInvoiceDto.setOcrContentModified(OcrContentModifiedEnum.MODIFIED.getCode());
        invoiceMapper.updateById(invoiceAssembler.toEntity(updatedInvoiceDto));

        // 发票金额变化,触发事件推送
        if (invoiceChangedAmount(sourceInvoiceDto, updatedInvoiceDto)) {
            eventPublisher.publishEvent(new InvoiceAmountChangedEvent(invoiceDto.getUuid()));
        }
    }

    private Boolean invoiceChangedAmount(InvoiceDto updateBefore, InvoiceDto updateAfter) {
        return !updateBefore.getAmount().equals(updateAfter.getAmount());
    }

    @Override
    public InvoiceEntity getInvoiceEntityByUuid(String uuid, boolean withDeleted) {
        LambdaQueryWrapper<InvoiceEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(InvoiceEntity::getUuid, uuid);
        if (!withDeleted) {
            queryWrapper.eq(InvoiceEntity::getStatus, RecordStatusEnum.VALID.getCode());
        }
        List<InvoiceEntity> invoiceEntities = invoiceMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(invoiceEntities)) {
            throw new BusinessException("发票不存在");
        }
        return invoiceEntities.get(0);
    }

    /**
     * 查询发票
     *
     * @param invoiceUuidList 发票ID列表
     * @return 发票列表
     */
    @Override
    public List<InvoiceDto> queryInvoice(List<String> invoiceUuidList) {
        return invoiceAssembler.toDtoList(queryInvoiceEntity(invoiceUuidList));
    }

    @Override
    public BigDecimal calculateInvoicesAmount(List<String> invoiceUuids) {
        if (CollectionUtils.isEmpty(invoiceUuids)) {
            return BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        }
        BigDecimal totalAmount = BigDecimal.ZERO;
        List<InvoiceDto> invoiceDtoList = queryInvoice(invoiceUuids);
        for (InvoiceDto invoiceDto : invoiceDtoList) {
            if (invoiceDto != null && invoiceDto.getAmount() != null) {
                totalAmount = totalAmount.add(invoiceDto.getAmount());
            }
        }
        return totalAmount.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 按条件查询发票
     *
     * @param invoiceQueryRuleDto 查询条件
     * @return
     */
    public List<String> queryInvoiceByRule(InvoiceQueryRuleDto invoiceQueryRuleDto) {
        // 用户ID
        String userId = invoiceQueryRuleDto.getUserId();
        // 匹配金额
        String matchAmount = invoiceQueryRuleDto.getAmount();
        // 金额差值
        BigDecimal matchAmountDiff = new BigDecimal(invoiceQueryRuleDto.getAmountDiff());
        // 查询开始时间
        String queryStartTime = invoiceQueryRuleDto.getStartTime();
        // 查询结束时间
        String queryEndTime = invoiceQueryRuleDto.getEndTime();
        // 发票状态列表
        List<Integer> invoiceStatusList = invoiceQueryRuleDto.getInvoiceStatusList();
        // 发票-费用状态列表
        List<Integer> invoiceExpenseStatusList = invoiceQueryRuleDto.getInvoiceExpenseStatusList();

        // 是否开启「金额匹配模式」
        boolean amountMatchMode = StringUtils.isNotBlank(matchAmount);
        // 是否开启「时间匹配模式」
        boolean timeMatchMode = StringUtils.isNotBlank(queryStartTime) && StringUtils.isNotBlank(queryEndTime);

        // 用户所有满足条件的发票
        LambdaQueryWrapper<InvoiceEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(InvoiceEntity::getCreatorUserId, userId).eq(InvoiceEntity::getStatus, RecordStatusEnum.VALID.getCode());
        if (CollectionUtils.isNotEmpty(invoiceStatusList)) {
            queryWrapper.in(InvoiceEntity::getInvoiceStatus, invoiceStatusList);
        }
        if (CollectionUtils.isNotEmpty(invoiceExpenseStatusList)) {
            queryWrapper.in(InvoiceEntity::getInvoiceExpenseStatus, invoiceExpenseStatusList);
        }
        List<InvoiceEntity> invoiceList = invoiceMapper.selectList(queryWrapper);

        // 遍历所有发票
        return invoiceList.stream()
                .filter(invoice -> {
                    JSONArray fields = JSONArray.parseArray(invoice.getDetails());
                    boolean amountMatchResult = true;
                    boolean timeMatchResult = true;

                    // 遍历本发票的动态表单
                    for (Object object : fields) {
                        JSONObject field = (JSONObject) object;

                        // 仅在开启「金额匹配模式」时才进行金额匹配
                        if (amountMatchMode && FIELD_CODE_TOTAL_AMOUNT.equals(field.getString(FIELD_CODE))) {
                            String invoiceAmount = field.getString(DEFAULT_VALUE);
                            // 保证「发票金额」、「匹配金额」格式正确
                            if (reimburseCommonService.amountFormatIsError(invoiceAmount) || reimburseCommonService.amountFormatIsError(matchAmount)) {
                                return false;
                            }

                            // 计算本发票金额是否匹配上
                            BigDecimal matchAmountBD = new BigDecimal(matchAmount);
                            BigDecimal invoiceAmountBD = new BigDecimal(invoiceAmount);
                            BigDecimal amountDiff = matchAmountBD.subtract(invoiceAmountBD).abs();
                            amountMatchResult = amountDiff.compareTo(matchAmountDiff) <= 0;
                        }

                        // 仅在开启「时间匹配模式」时才进行匹配
                        if (timeMatchMode && FIELD_CODE_INVOICE_DATE.equals(field.getString(FIELD_CODE))) {
                            String invoiceDate = field.getString(DEFAULT_VALUE);
                            // 保证「发票时间」、「匹配开始时间」、「匹配结束时间」格式正确
                            if (reimburseCommonService.dateFormatIsError(invoiceDate) || reimburseCommonService.dateFormatIsError(queryStartTime) || reimburseCommonService.dateFormatIsError(queryEndTime)) {
                                return false;
                            }

                            // 计算本发票时间是否匹配上
                            timeMatchResult = reimburseCommonService.timeInTimeRange(invoiceDate, queryStartTime, queryEndTime);
                        }
                    }

                    // 本发票是否匹配上
                    return amountMatchResult && timeMatchResult;
                })
                .map(InvoiceEntity::getUuid)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceEntity> queryInvoiceEntity(List<String> invoiceUuidList) {
        if (CollectionUtils.isEmpty(invoiceUuidList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<InvoiceEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(InvoiceEntity::getUuid, invoiceUuidList);
        return invoiceMapper.selectList(queryWrapper);
    }

    /**
     * 唯一性校验
     * （1）由于发票有「待补充」状态，即发票允许三个唯一性字段为空，所以无需进行必填字段校验
     *
     * @param dto 待校验发票实体
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "oaOfficeTransactionManager")
    public void uniqueCheck(InvoiceDto dto) {
        // 特殊逻辑：「其他票」由于没有invoiceNo和invoiceType，因此不做校验
        if (StringUtils.equals(dto.getTicketType(), InvoiceTypeEnum.OTHER.getCode())) {
            return;
        }

        List<InvoiceEntity> entityList = queryInvoiceList(dto.getInvoiceNo(), dto.getInvoiceType(), dto.getInvoiceCode());

        List<String> userIdList = entityList.stream().map(InvoiceEntity::getCreatorUserId).collect(Collectors.toList());
        String userId = dto.getCreatorUserId();

        // 未被添加过-正常
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }

        // 自己已经添加过-异常
        if (userIdList.contains(userId)) {
            throw new BusinessException("该发票已被当前用户添加过");
        }

        // 别人已经报过-异常
        else {
            throw new BusinessException("该发票已被其他用户添加过");
        }
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = "oaOfficeTransactionManager")
    public List<InvoiceEntity> queryInvoiceList(String invoiceNo, String invoiceType, String invoiceCode) {
        LambdaQueryWrapper<InvoiceEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(InvoiceEntity::getInvoiceType, StringUtils.isBlank(invoiceType) ? "" : invoiceType);
        queryWrapper.eq(InvoiceEntity::getInvoiceCode, StringUtils.isBlank(invoiceCode) ? "" : invoiceCode);
        queryWrapper.eq(InvoiceEntity::getInvoiceNo, StringUtils.isBlank(invoiceNo) ? "" : invoiceNo);
        queryWrapper.eq(InvoiceEntity::getStatus, RecordStatusEnum.VALID.getCode());
        return invoiceMapper.selectList(queryWrapper);
    }

    /**
     * 匹配发票
     * 时间匹配规则：
     * （1）水单 match 发票 time = time
     * <p>
     * 一般费用报销单：amount(diff)
     * 差旅报销单：amount(diff) && time(=)
     *
     * @param invoiceMatchRuleDto 匹配规则
     * @return 匹配到的费用ID列表
     */
    public List<String> matchInvoice(InvoiceMatchRuleDto invoiceMatchRuleDto) {
        // 用户ID
        String userId = invoiceMatchRuleDto.getUserId();
        // 匹配金额
        String matchAmount = invoiceMatchRuleDto.getAmount();
        // 金额差值
        BigDecimal matchAmountDiff = new BigDecimal(invoiceMatchRuleDto.getAmountDiff());
        // 匹配时间
        String matchTime = invoiceMatchRuleDto.getTime();
        // 发票状态列表
        List<Integer> invoiceStatusList = invoiceMatchRuleDto.getInvoiceStatusList();
        // 发票-费用状态列表
        List<Integer> invoiceExpenseStatusList = invoiceMatchRuleDto.getInvoiceExpenseStatusList();

        // 是否开启「金额匹配模式」
        boolean amountMatchMode = StringUtils.isNotBlank(matchAmount);
        // 是否开启「时间匹配模式」
        boolean timeMatchMode = StringUtils.isNotBlank(matchTime);

        // 用户所有满足条件的发票
        LambdaQueryWrapper<InvoiceEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(InvoiceEntity::getCreatorUserId, userId).eq(InvoiceEntity::getStatus, RecordStatusEnum.VALID.getCode());
        if (CollectionUtils.isNotEmpty(invoiceStatusList)) {
            queryWrapper.in(InvoiceEntity::getInvoiceStatus, invoiceStatusList);
        }
        if (CollectionUtils.isNotEmpty(invoiceExpenseStatusList)) {
            queryWrapper.in(InvoiceEntity::getInvoiceExpenseStatus, invoiceExpenseStatusList);
        }
        List<InvoiceEntity> invoiceList = invoiceMapper.selectList(queryWrapper);

        // 遍历所有发票
        return invoiceList.stream()
                .filter(invoice -> {
                    JSONArray fields = JSONArray.parseArray(invoice.getDetails());
                    boolean amountMatchResult = true;
                    boolean timeMatchResult = true;

                    // 遍历本发票的动态表单
                    for (Object object : fields) {
                        JSONObject field = (JSONObject) object;

                        // 仅在开启「金额匹配模式」时才进行金额匹配
                        if (amountMatchMode && FIELD_CODE_TOTAL_AMOUNT.equals(field.getString(FIELD_CODE))) {
                            String invoiceAmount = field.getString(DEFAULT_VALUE);
                            // 保证「发票金额」、「匹配金额」格式正确
                            if (reimburseCommonService.amountFormatIsError(invoiceAmount) || reimburseCommonService.amountFormatIsError(matchAmount)) {
                                return false;
                            }

                            // 计算本发票金额是否匹配上
                            BigDecimal matchAmountBD = new BigDecimal(matchAmount);
                            BigDecimal invoiceAmountBD = new BigDecimal(invoiceAmount);
                            BigDecimal amountDiff = matchAmountBD.subtract(invoiceAmountBD).abs();
                            amountMatchResult = amountDiff.compareTo(matchAmountDiff) <= 0;
                        }

                        // 仅在开启「时间匹配模式」时才进行匹配
                        if (timeMatchMode && FIELD_CODE_INVOICE_DATE.equals(field.getString(FIELD_CODE))) {
                            String invoiceDate = field.getString(DEFAULT_VALUE);
                            // 保证「发票时间」、「匹配开始时间」、「匹配结束时间」格式正确
                            if (reimburseCommonService.dateFormatIsError(invoiceDate) || reimburseCommonService.dateFormatIsError(matchTime)) {
                                return false;
                            }

                            // 计算本发票时间是否匹配上
                            timeMatchResult = matchTime.equals(invoiceDate);
                        }
                    }

                    // 本发票是否匹配上
                    return amountMatchResult && timeMatchResult;
                })
                .map(InvoiceEntity::getUuid)
                .collect(Collectors.toList());
    }

    /**
     * @param userId          用户ID
     * @param invoiceUuIdList 发票ID列表
     */
    @Override
    public void existCheck(String userId, List<String> invoiceUuIdList) {
        List<InvoiceEntity> invoiceList = queryInvoiceEntity(invoiceUuIdList);
        if (CollectionUtils.isEmpty(invoiceList)) {
            throw new BusinessException("未找到该发票");
        }

        List<String> userIdList = invoiceList.stream().map(InvoiceEntity::getCreatorUserId).distinct().collect(Collectors.toList());
        if (userIdList.size() != 1 || !userIdList.contains(userId)) {
            throw new BusinessException("非本人发票");
        }
    }

    @Override
    public void usedCheck(List<String> invoiceUuIdList) {
        List<InvoiceEntity> invoiceList = queryInvoiceEntity(invoiceUuIdList);
        if (CollectionUtils.isEmpty(invoiceList)) {
            return;
        }

        boolean hasUsedInvoice = invoiceList.stream().anyMatch(invoice -> InvoiceExpenseStatusEnum.YGL.getCode() == invoice.getInvoiceExpenseStatus());
        if (hasUsedInvoice) {
            throw new BusinessException("发票已被使用");
        }
    }

    @Override
    public void updateInvoiceListStatus(List<String> invoiceUuidList, InvoiceStatusEnum invoiceStatus, InvoiceExpenseStatusEnum invoiceExpenseStatus, InvoiceFormStatusEnum invoiceFormStatusEnum) {
        List<InvoiceEntity> invoiceEntityList = queryInvoiceEntity(invoiceUuidList);
        invoiceEntityList.forEach(invoiceEntity -> {
            if (invoiceStatus != null) {
                invoiceEntity.setInvoiceStatus(invoiceStatus.getCode());
            }

            if (invoiceExpenseStatus != null) {
                invoiceEntity.setInvoiceExpenseStatus(invoiceExpenseStatus.getCode());
            }

            if (invoiceFormStatusEnum != null) {
                invoiceEntity.setInvoiceFormStatus(invoiceFormStatusEnum.getCode());
            }

            invoiceMapper.updateById(invoiceEntity);
        });
    }

    @Override
    public void invoiceDateConvertToDotSeparatedDate(List<InvoiceDto> invoiceDtoList) {
        if (CollectionUtils.isEmpty(invoiceDtoList)) {
            return;
        }

        invoiceDtoList.stream().forEach(dto -> {
            dto.setInvoiceDate(DateUtil.convertToDotSeparatedDate(dto.getInvoiceDate()));

            Object invoiceDateObject = dynamicFormFieldService.getDefaultValueObject(dto.getDynamicFormFieldDtoList(), CommonConstant.INVOICE_FIELD_ID_CODE_INVOICE_DATE);
            if (!dynamicFormFieldService.isValueEmpty(invoiceDateObject)) {
                String date = (String) invoiceDateObject;
                dynamicFormFieldService.updateDynamicFromDefaultValue(dto.getDynamicFormFieldDtoList(), CommonConstant.INVOICE_FIELD_ID_CODE_INVOICE_DATE, DateUtil.convertToDotSeparatedDate(date));
            }
        });
    }

    @Override
    public void checkReimbursementFormInInvoice(ReimbursementFormCheckResultDto result, List<InvoiceDto> invoiceList) {
        // 拿到所有的校验项
        List<CheckResultDetailDto> checkResultDetailDtoList = invoiceList.stream()
                .map(InvoiceDto::getItineraryCheckResultDto)
                .filter(Objects::nonNull)
                .flatMap(checkResult -> Stream.of(
                        checkResult.getBlockSubmissionCheckResultList(),
                        checkResult.getAllowSubmissionCheckResultList()
                ))
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        result.getCheckResultDetailList().addAll(checkResultDetailDtoList);

        // 发票完整性校验
        invoiceIntegrityCheck(result, invoiceList);
    }

    /**
     * 发票完整性校验
     * @param result 校验结果
     * @param invoiceList 发票列表
     */
    private void invoiceIntegrityCheck(ReimbursementFormCheckResultDto result, List<InvoiceDto> invoiceList) {
        List<BlockItemDto> blockItemList = new ArrayList<>();
        InvoiceCheckItemEnum checkItem = InvoiceCheckItemEnum.INVOICE_INTEGRITY_CHECK;
        List<DynamicFormFieldDto> dynamicFormFieldDtoList = invoiceList.stream()
                .map(InvoiceDto::getDynamicFormFieldDtoList)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        // 校验动态表单
        for (DynamicFormFieldDto field : dynamicFormFieldDtoList) {
            // 如果是必填项且默认值为空，则添加到阻碍项列表
            if (field.isMust() && dynamicFormFieldService.isValueEmpty(field.getDefaultValue())) {
                blockItemList.add(BlockItemDto.builder()
                        .fieldCode(field.getFieldCode())
                        .fieldName(field.getFieldName())
                        .build());
            }
        }

        // 有阻碍项
        if (CollectionUtils.isNotEmpty(blockItemList)) {
            CheckResultDetailDto detail = CheckResultDetailDto.builder()
                    .checkItemCode(checkItem.getCheckItemCode())
                    .checkItemName(checkItem.getCheckItemName())
                    .blockSubmit(checkItem.getBlockSubmit())
                    .blockItemList(blockItemList)
                    .build();
            result.getCheckResultDetailList().add(detail);
        }
    }
}
