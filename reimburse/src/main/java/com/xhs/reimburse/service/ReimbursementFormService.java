package com.xhs.reimburse.service;

import com.xhs.reimburse.enums.ReimbursementFormStatusEnum;
import com.xhs.reimburse.modal.dto.ReimbursementFormDto;
import com.xhs.reimburse.modal.dto.ReimbursementFormSimpleDto;
import com.xhs.reimburse.modal.dto.ReimbursementFormCheckResultDto;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.modal.request.PageQueryReimburseFormRequest;
import com.xiaohongshu.erp.common.framework.page.PageResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/02/12 - 下午2:43
 * @description :
 */
public interface ReimbursementFormService {

    Integer getPendingCount();

    String addReimbursementForm(ReimbursementFormDto dto);

    List<ReimbursementFormEntity> queryReimbursementFormEntity(List<String> reimbursementFormUuidList);

    List<ReimbursementFormDto> queryReimbursementForm(List<String> reimbursementFormUuidList);

    void existCheck(String userId, List<String> reimbursementFormUuidList);

    /**
     * 提交报销单，新增和更新表单内容
     */
    void addOrUpdateReimbursementForm4Submit(ReimbursementFormEntity reimburseForm);

    PageResult<ReimbursementFormSimpleDto> pageQuery(PageQueryReimburseFormRequest request);

    void updateReimbursementFormAllStatus(String formNum, ReimbursementFormStatusEnum reimbursementFormStatus);

    ReimbursementFormEntity getReimbursementFormByFormNum(String formNum, boolean withDeleted);

    /**
     * 删除redFlow单据时，作废本地库数据
     *
     * @param formNum redFlow单据号
     */
    void invalidReimbursementForm(String formNum);

    ReimbursementFormEntity findSelfReimburseForm(String formNum, String userId);

    ReimbursementFormEntity findReimburseForm(String formNum);

    void invalidReimbursementFormByUuid(String uuid);

    void updateReimbursementFormListStatus(List<String> reimbursementFormUuidList, ReimbursementFormStatusEnum reimbursementFormStatus);

    String getPaymentCompanyName(ReimbursementFormEntity reimbursementFormEntity);

    void updateReimbursementFormRelationFild(String reimbursementFormUuid, List<String> expenseUuidList);

    ReimbursementFormCheckResultDto checkReimbursementForm(String formNum, String userId);

    boolean formNeedAudit(String formNum);
}
