package com.xhs.reimburse.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhs.reimburse.constant.CommonConstant;
import com.xhs.reimburse.enums.ExpenseSecondSubjectEnum;
import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.RedFlowMsgDTO;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.modal.request.RedFlowProcessSaveOrStartRequest;
import com.xhs.reimburse.modal.request.ReimburseFormRequest;
import com.xhs.reimburse.modal.response.ReimbursementFormResponse;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @desc 一般费用报销单
 */
@Service(value = "YBFYBXD" + CommonConstant.FORM_COMPONENT_SUFFIX)
public class CommonFormService extends ReimburseFormFlowAdapter {

    @Override
    public ReimbursementFormResponse buildReimbursementForm(ReimbursementFormEntity reimburseForm, ReimburseFormRequest formRequest) {
        return super.buildReimbursementForm(reimburseForm, formRequest);
    }


    @Override
    public String checkFormData(ReimburseFormRequest formRequest) {
        List<ExpenseDto> expenses = formRequest.getExpenses();
        if (CollUtil.isEmpty(expenses)) {
            return "提交时费用不能为空";
        }

        List<String> secondSubjects = expenses.stream().map(ExpenseDto::getSecondSubject).distinct().collect(Collectors.toList());

        if (secondSubjects.contains(ExpenseSecondSubjectEnum.STHD.getSubjectCode()) && secondSubjects.size() != 1) {
            return "报销明细选取不符合规定，社团活动不能与其他活动一并提交";
        }

        if (secondSubjects.contains(ExpenseSecondSubjectEnum.YWZDCY.getSubjectCode())) {
            if (secondSubjects.size() != 1) {
                return "业务招待费-餐饮不可与其他费用合并报销，请分单据报销";
            }
        }
        return "";
    }

    @Override
    public RedFlowProcessSaveOrStartRequest buildVariableMap(ReimburseFormRequest formRequest, boolean save) {

        RedFlowProcessSaveOrStartRequest request = super.buildVariableMap(formRequest, save);
        if (save) {
            return request;
        }

        Map<String, Object> variableMap = request.getVariableMap();
        //工作地
        variableMap.put("formCity", formRequest.getCreatorInfo().getWorkplace());
        //餐饮是否超标
        boolean mealStandardExceed = false;
        List<ExpenseDto> expenses = formRequest.getExpenses();
        if (CollUtil.isNotEmpty(expenses)) {
            //二级科目
            ExpenseDto firstExpense = expenses.get(0);
            variableMap.put("expenseSubject", firstExpense.getSecondSubject());

            for (ExpenseDto expense : expenses) {
                Map<String, String> exceptionDetailsMap = expense.getExceptionDetailsMap();
                if (MapUtils.isNotEmpty(exceptionDetailsMap) && exceptionDetailsMap.containsKey("waterLevel")) {
                    mealStandardExceed = true;
                    break;
                }
            }

        }
        variableMap.put("mealStandardExceed", String.valueOf(mealStandardExceed));

        return request;
    }

    /**
     * 获取表单校验状态
     *
     * @param request 请求信息
     * @return 是否可提报 true 可提报
     */
    @Override
    public boolean getFormCheckedStatus(ReimburseFormRequest request) {

        if (!super.getFormCheckedStatus(request)) {
            return false;
        }

        return StrUtil.isBlank(this.checkFormData(request));
    }

    @Override
    public void formInReview(String formNum, RedFlowMsgDTO redFlowMsgDTO) {
        paymentRedFlowService.flowCausePayment(formNum, redFlowMsgDTO);
    }
}