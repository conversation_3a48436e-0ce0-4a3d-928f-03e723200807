package com.xhs.reimburse.service.impl;

import com.google.common.collect.Lists;
import com.xhs.ehr.rpc.request.QueryEmployeeRequest;
import com.xhs.ehr.rpc.response.BatchQueryEmployeeResponse;
import com.xhs.ehr.rpc.response.EmployeeInfo;
import com.xhs.ehr.rpc.response.EmployeeRecordInfo;
import com.xhs.ehr.rpc.service.EhrEmployeeService;
import com.xhs.finance.sso.UserInfoBag;
import com.xhs.finance.utils.AssertUtils;
import com.xhs.oa.office.rpc.EmployeeRecordService;
import com.xhs.reimburse.assembler.BankAccountAssembler;
import com.xhs.reimburse.mapper.BankAccountMapper;
import com.xhs.reimburse.modal.dto.BankAccountDto;
import com.xhs.reimburse.modal.entity.BankAccountEntity;
import com.xhs.reimburse.modal.request.BankAccountRequest;
import com.xhs.reimburse.service.BankAccountService;
import com.xiaohongshu.fls.rpc.himalaya.HimalayaService;
import com.xiaohongshu.fls.rpc.himalaya.response.GetBankCodeByNameResponse;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date :2025/02/07 - 下午8:40
 * @description :
 */
@Slf4j
@Service
public class BankAccountServiceImpl implements BankAccountService {

    // RPC
    @Autowired
    private EhrEmployeeService.Iface ehrEmployeeService;

    @Resource
    private HimalayaService.Iface himalayaService;

    @Resource
    private BankAccountMapper tbankAccountMapper;

    @Resource
    private BankAccountAssembler bankAccountAssembler;
    @Autowired
    private EmployeeRecordService employeeRecordService;
    // 常量
    private static final Long ENTERPRISE_ID = 10000L;


    /**
     * 查询员工的报销的银行卡号列表
     * 首次进入为空，并从人事系统同步落库
     *
     * @param userId 核心人事用户编号
     * @return List<BankAccountDto>
     */
    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "oaOfficeTransactionManager")
    public List<BankAccountDto> getBankAccountList(String userId) {
        List<BankAccountEntity> bankAccountList = tbankAccountMapper.getUserBankAccountList(userId);
        if (CollectionUtils.isEmpty(bankAccountList)) {
            //首次拉取从人事获取用户的银行卡号信息并落库
            BankAccountEntity bankAccountFromEhr = getBankAccountFromEhr(userId);
            if (Objects.nonNull(bankAccountFromEhr)) {
                tbankAccountMapper.addBankAccount(bankAccountFromEhr);
                bankAccountList = Arrays.asList(bankAccountFromEhr);
            }
        } else {
            bankAccountList = bankAccountList.stream()
                    .filter(bankAccount -> Objects.equals(bankAccount.getIsValid(), 1))
                    .collect(Collectors.toList());
        }
        return bankAccountAssembler.toDtoList(bankAccountList);
    }

    /**
     * 新增或者编辑银行账户信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "oaOfficeTransactionManager")
    public void addOrUpdate(BankAccountRequest bankAccountRequest) {
        BankAccountEntity bankAccountEntity
                = bankAccountAssembler.reqToEntity(bankAccountRequest, UserInfoBag.get().getUserId());
        AssertUtils.notNull(bankAccountEntity, "bankAccountRequest is null");
        if (Objects.nonNull(bankAccountEntity.getId())) {
            // id = #{id} and user_id = #{userId}
            tbankAccountMapper.updateBankAccountById(bankAccountEntity);
        } else {
            tbankAccountMapper.addBankAccount(bankAccountEntity);
        }
        // 当bankAccountEntity isDefault=1时，则判断当前用户是否存在其他银行信息，如果存在则将其他银行信息isDefault设置为0
        if (Objects.equals(bankAccountEntity.getIsDefault(), 1)) {
            updateOtherBankAccountIsNotDefault(bankAccountEntity.getUserId(), bankAccountEntity.getId());
        }
    }

    private void updateOtherBankAccountIsNotDefault(String userId, Long id) {
        List<BankAccountEntity> bankAccountList = tbankAccountMapper.getUserBankAccountList(userId);
        if (CollectionUtils.isEmpty(bankAccountList)) return;
        bankAccountList = bankAccountList.stream()
                .filter(bankAccount -> !bankAccount.getId().equals(id))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bankAccountList)) return;
        bankAccountList.forEach(bankAccount -> {
            bankAccount.setIsDefault(0);
            tbankAccountMapper.updateBankAccountById(bankAccount);
        });
    }

    //从人事获取用户的银行卡号信息
    private BankAccountEntity getBankAccountFromEhr(String userId) {
        QueryEmployeeRequest request = new QueryEmployeeRequest();
        request.setEnterpriseId(ENTERPRISE_ID);
        request.setEmployeeIdList(Collections.singletonList(Long.valueOf(userId)));
        BatchQueryEmployeeResponse response = null;

        try {
            response = ehrEmployeeService.query_employee_by_ids(new Context(), request);
            if (response.success && CollectionUtils.isNotEmpty(response.getEmployeeInfoList())) {
                EmployeeInfo employeeInfo = response.getEmployeeInfoList().get(0);
                List<EmployeeRecordInfo> employeeRecordInfos = employeeRecordService.queryCurrentEmployeeRecordByStrIds(Lists.newArrayList(userId));
                if (CollectionUtils.isNotEmpty(employeeRecordInfos)) { // 竞业员工设置为空
                    byte competeFlag = employeeRecordInfos.get(0).getCompeteFlag();
                    if (competeFlag == 1) { // 竞业不设置姓名
                        employeeInfo.setUserName("");
                    }
                }
                GetBankCodeByNameResponse bankCode = himalayaService.getBankCodeByName(new Context(), employeeInfo.getBankName().trim());
                return bankAccountAssembler.toBankAccountEntity(employeeInfo, bankCode.getCode());
            }
        } catch (Exception e) {
            log.error("BankAccountServiceImpl getBankAccountFromEhr error: {}", e.getMessage(), e);
        }
        return null;
    }
}
