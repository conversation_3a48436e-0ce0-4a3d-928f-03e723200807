package com.xhs.reimburse.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.reimburse.constant.CommonConstant;
import com.xhs.reimburse.constant.TravelConstant;
import com.xhs.reimburse.enums.ExpenseFirstSubjectEnum;
import com.xhs.reimburse.enums.ExpenseFormTypeEnum;
import com.xhs.reimburse.enums.ExpenseSecondSubjectEnum;
import com.xhs.reimburse.enums.travel.ExpenseCheckItemEnum;
import com.xhs.reimburse.modal.dto.DynamicFormFieldDto;
import com.xhs.reimburse.modal.dto.ExpenseDto;
import com.xhs.reimburse.modal.dto.CheckResultDetailDto;
import com.xhs.reimburse.modal.dto.travel.ExpenseContentDto;
import com.xhs.reimburse.modal.dto.travel.SeatContentDto;
import com.xhs.reimburse.modal.dto.travel.TravelCityDto;
import com.xhs.reimburse.modal.entity.travel.TravelStandard;
import com.xhs.reimburse.modal.request.travel.ExpenseBudgetCheckRequest;
import com.xhs.reimburse.modal.request.travel.SeatIsOverBudgetRequest;
import com.xhs.reimburse.modal.vo.travel.CTripCountryVO;
import com.xhs.reimburse.rpc.consumer.HimalayaRpcService;
import com.xhs.reimburse.service.CheckOverBudgetService;
import com.xhs.reimburse.service.InvoiceService;
import com.xhs.reimburse.service.TravelPlaceService;
import com.xhs.reimburse.service.TravelStandardService;
import com.xhs.reimburse.utils.StringUtil;
import com.xiaohongshu.erp.common.exception.BusinessException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CheckOverBudgetServiceImpl.java
 * @createTime 2025年03月20日 20:50:00
 */
@Slf4j
@Data
@Service
public class CheckOverBudgetServiceImpl implements CheckOverBudgetService {

    @Resource
    private TravelPlaceService travelPlaceService;
    @Resource
    private InvoiceService invoiceService;
    @Resource
    private DynamicFormFieldServiceImpl dynamicFormFieldService;
    @Resource
    private TravelStandardService travelStandardService;
    @Resource
    private HimalayaRpcService himalayaRpcService;

    @ApolloConfig
    private Config apolloConfig;
    @ApolloJsonValue("${travel_support_first_level_cities:{}}")
    private Map<String, List<String>> supportFirstLevelCities;

    // 工作餐费用预算配置
    @ApolloJsonValue("${expense_meal_budget_config:{}}")
    private Map<String, Integer> businessMealBudgetMap;

    /**
     * 酒店住宿费用预算配置
     */
    private static final String EXPENSE_BUDGET_CONFIG = "expense_budget_config";
    // 美元
    private static final String DOLLAR_CODE = "USD";
    private static final String SEAT_CHECK_TYPE_TRAIN = "train";
    private static final String SEAT_CHECK_TYPE_AIR = "air";

    @Override
    public void checkExpenseOverBudget(List<CheckResultDetailDto> allowList, List<CheckResultDetailDto> blockList, ExpenseDto expenseDto) {
        if (Objects.isNull(expenseDto) || StringUtils.isEmpty(expenseDto.getFormType()) || StringUtils.isEmpty(expenseDto.getFirstSubject())) {
            return;
        }

        // 飞机/火车票超标校验
        List<String> trainAirSubjectCodes = Arrays.asList(ExpenseFirstSubjectEnum.CL_TRAIN_TICKET.getSubjectCode(), ExpenseFirstSubjectEnum.CL_AIR_TICKET.getSubjectCode());
        if (ExpenseFormTypeEnum.CL.getFormTypeCode().equals(expenseDto.getFormType()) && !StringUtils.isEmpty(expenseDto.getFirstSubject()) && trainAirSubjectCodes.contains(expenseDto.getFirstSubject())) {
            SeatIsOverBudgetRequest request = buildSeatIsOverBudgetRequest(expenseDto);
            if (request == null) {
                return;
            }
            List<SeatContentDto> seatCheckList = seatIsOverBudget(request);
            for (SeatContentDto seatContentDto : seatCheckList) {
                if (seatContentDto.getIsOverBudget()) {
                    CheckResultDetailDto checkResultItemDto = ExpenseCheckItemEnum.getChckResultDetail(ExpenseCheckItemEnum.EXPENSE_SEAT_STANDARD_DIFFERENCE_CHECK);
                    String checkText = ExpenseFirstSubjectEnum.CL_TRAIN_TICKET.getSubjectCode().equals(expenseDto.getFirstSubject()) ? "火车票" : "飞机票";
                    checkResultItemDto.setDescription(String.format(checkResultItemDto.getDescription(), checkText));
                    allowList.add(checkResultItemDto);
                    return;
                }
            }
        }

        // 差旅工作餐
        if (ExpenseFormTypeEnum.CL.getFormTypeCode().equals(expenseDto.getFormType()) && ExpenseFirstSubjectEnum.CL_BUSINESS_MEAL.getSubjectCode().equals(expenseDto.getFirstSubject())) {
            ExpenseBudgetCheckRequest request = buildBusinessMealRequest(expenseDto);
            if (checkBusinessMealOverBudget(request)) {
                allowList.add(ExpenseCheckItemEnum.getChckResultDetail(ExpenseCheckItemEnum.EXPENSE_COMMON_STANDARD_DIFFERENCE_CHECK));
            }
        }

        // 酒店
        if (ExpenseFormTypeEnum.CL.getFormTypeCode().equals(expenseDto.getFormType()) && ExpenseFirstSubjectEnum.CL_HOTEL.getSubjectCode().equals(expenseDto.getFirstSubject())) {
            ExpenseContentDto expenseContentDto = buildHotelRequest(expenseDto);
            if (expenseContentDto != null && checkTravelHotelOverBudget(expenseDto.getSecondSubject(), expenseContentDto)) {
                allowList.add(ExpenseCheckItemEnum.getChckResultDetail(ExpenseCheckItemEnum.EXPENSE_COMMON_STANDARD_DIFFERENCE_CHECK));
            }
        }

        // 差旅报销单-油电费用超标校验
        if (fuelParkingIsOverBudget(expenseDto)) {
            blockList.add(ExpenseCheckItemEnum.getChckResultDetail(ExpenseCheckItemEnum.EXPENSE_EXPENSE_EXCEED_REIMBURSABLE_LIMIT));
        }
    }

    @Override
    public Boolean fuelParkingIsOverBudget(ExpenseDto expenseDto) {
        List<String> fuelSubjectList = Arrays.asList(
                ExpenseFirstSubjectEnum.CL_FUEL_PARKING.getSubjectCode(),
                ExpenseFirstSubjectEnum.CL_FUEL_ELECTRIC_OTHER_FEE.getSubjectCode()
        );
        if (ExpenseFormTypeEnum.CL.getFormTypeCode().equals(expenseDto.getFormType()) && fuelSubjectList.contains(expenseDto.getFirstSubject())) {
            List<DynamicFormFieldDto> dynamicFormFieldDtoList = expenseDto.getDynamicFormFieldDtoList();
            Object reimbursableAmountObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_REIMBURSABLE_AMOUNT);
            Object amountObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_AMOUNT);
            if (!dynamicFormFieldService.isValueEmpty(reimbursableAmountObject) && !dynamicFormFieldService.isValueEmpty(amountObject)) {
                BigDecimal reimbursableAmount = new BigDecimal((String) reimbursableAmountObject);
                BigDecimal amount = new BigDecimal((String) amountObject);
                if (amount.compareTo(reimbursableAmount) > 0) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public List<SeatContentDto> seatIsOverBudget(SeatIsOverBudgetRequest request) {
        if (request == null || CollectionUtils.isEmpty(request.getCheckList())) {
            return Collections.emptyList();
        }

        for (SeatContentDto seatContentDto : request.getCheckList()) {
            if (SEAT_CHECK_TYPE_TRAIN.equals(seatContentDto.getCheckType())) {
                checkTrainSeatTypeOverBudget(seatContentDto, request.getFormType(), ExpenseFirstSubjectEnum.CL_TRAIN_TICKET.getSubjectCode());
            }

            if (SEAT_CHECK_TYPE_AIR.equals(seatContentDto.getCheckType())) {
                checkFlightSeatTypeOverBudget(seatContentDto, ExpenseFirstSubjectEnum.CL_AIR_TICKET.getSubjectCode());
            }
        }

        return request.getCheckList();
    }


    public Boolean checkTravelHotelOverBudget(String secondSubject, ExpenseContentDto expenseContentDto) {
        //历史国外数据不计算是否超标 默认不超预算
        if (StringUtils.isBlank(expenseContentDto.getCityName()) || expenseContentDto.getCityName().contains(TravelConstant.OVERSEAS)) {
            return false;
        }

        CTripCountryVO countryInfo = travelStandardService.selectCountryInfoByCountryName(expenseContentDto.getCountryName());
        //城市code包含字母说明保存的时flight类型的地点code 需要转换
        String placeCode = StringUtil.match(StringUtil.azAzPattern, expenseContentDto.getCity()) ? travelPlaceService.convertPlaceCode(TravelConstant.TYPE_HOTEL, expenseContentDto.getCityName(), countryInfo.getCountryId()) : expenseContentDto.getCity();

        //是否使用同住条件
        String checkPartner = expenseContentDto.hotelNeedPartner(secondSubject);
        Integer travelDays = expenseContentDto.getTravelDays();
        //差标预算总金额
        BigDecimal hotelBudgetStandard = getTravelBudgetStandard(placeCode, travelDays, countryInfo.getCountryId(), checkPartner, ExpenseFirstSubjectEnum.CL_HOTEL.getSubjectCode());
        AssertHelper.notNull(hotelBudgetStandard, "差标金额为空");

        //本次预算总金额金额 用于校验
        return expenseContentDto.isOverBudget(hotelBudgetStandard);
    }

    public BigDecimal getTravelBudgetStandard(String cityId, Integer travelDays, String countryId, String hasPartner, String subject) {
        TravelStandard travelStandard = getCommonTravelStandard(cityId, countryId, hasPartner, subject);
        BigDecimal totalBudget = travelStandard.getAmount().multiply(new BigDecimal(travelDays));
        BigDecimal exchangeRate = himalayaRpcService.getExchangeRate(travelStandard.getCurrency());
        return totalBudget.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);
    }

    private TravelStandard getCommonTravelStandard(String cityId, String countryId, String hasPartner, String subject) {
        List<TravelStandard> travelStandardByCity = null;
        List<TravelStandard> cityAndCountryList = null;
        if (StringUtils.isNotBlank(cityId)) {
            travelStandardByCity = travelStandardService.selectTravelBudgetStandardByCity(subject, cityId);
            cityAndCountryList = travelStandardByCity.stream().filter(ts -> ts.getCountryId().equals(countryId)).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(travelStandardByCity) || CollectionUtils.isEmpty(cityAndCountryList)) {
            cityAndCountryList = travelStandardService.selectBudgetStandardByCountryId(subject, countryId);
        }

        if (CollectionUtils.isEmpty(cityAndCountryList)) {
            log.error("【差标配置】城市ID：" + cityId + "国家ID：" + countryId + " 差标配置为空");
            throw new BusinessException("差标配置为空，请联系OA薯更新差标: city " + cityId + " country " + countryId);
        }

        List<TravelStandard> cityAndCountryPaertnerList = cityAndCountryList.stream().filter(ts -> (StringUtils.isNotBlank(hasPartner) && hasPartner.equals(ts.getIsTogether()))).collect(Collectors.toList());

        return CollectionUtils.isNotEmpty(cityAndCountryPaertnerList) ? cityAndCountryPaertnerList.get(0) : cityAndCountryList.get(0);
    }


    /**
     * 校验火车座位是否超预算，在配置座位内说明不超预算
     *
     * @param seat 火车票信息
     * @return 当前车票是否超预算，true为超预算。
     */
    private void checkTrainSeatTypeOverBudget(SeatContentDto seat, String formType, String firstSubject) {
        if (Objects.isNull(seat) || StringUtils.isBlank(seat.getSeatType())) {
            return;
        }

        //工作地间往返
        boolean workPlaceReturn = false;
        List<String> cities = supportFirstLevelCities.get(formType);
        if (CollectionUtils.isNotEmpty(cities)) {
            String startCity = StringUtils.isEmpty(seat.getStartCity()) ? "" : seat.getStartCity();
            String endCity = StringUtils.isEmpty(seat.getEndCity()) ? "" : seat.getEndCity();
            workPlaceReturn = cities.contains(startCity) && cities.contains(endCity);
        }

        String configKey = workPlaceReturn ? "workplace_return_train_ticket" : firstSubject;
        JSONArray expenseBudgetConfig = getExpenseBudget(configKey);
        AssertHelper.notNull(expenseBudgetConfig, "未查询到火车票预算配置，请联系OA薯");

        seat.setIsOverBudget(!expenseBudgetConfig.toJavaList(String.class).contains(seat.getSeatType()));
    }

    private void checkFlightSeatTypeOverBudget(SeatContentDto seat, String firstSubject) {
        if (Objects.isNull(seat) || StringUtils.isBlank(seat.getSeatType()) || StringUtils.isBlank(firstSubject)) {
            return;
        }

        JSONArray expenseBudget = getExpenseBudget(firstSubject);
        AssertHelper.notNull(expenseBudget, "未查询到飞机票预算配置，请联系OA薯");
        List<String> seatTypesConfig = expenseBudget.toJavaList(String.class);
        seat.setIsOverBudget(!seatTypesConfig.contains(seat.getSeatType()));
    }

    public JSONArray getExpenseBudget(String firstSubject) {
        if (StringUtils.isBlank(firstSubject)) {
            return null;
        }
        String property = apolloConfig.getProperty(EXPENSE_BUDGET_CONFIG, "[]");
        JSONArray jsonArray = JSONObject.parseArray(property);
        for (JSONObject jsonObject : jsonArray.toJavaList(JSONObject.class)) {
            String firstSubjectConfig = jsonObject.getString("firstSubject");
            if (firstSubject.equals(firstSubjectConfig)) {
                return jsonObject.getJSONArray("budget");
            }
        }
        return null;
    }


    /**
     * 差旅工作餐
     *
     * @param request
     * @return
     */
    public Boolean checkBusinessMealOverBudget(ExpenseBudgetCheckRequest request) {
        if (request == null) {
            return false;
        }

        String secondSubject = request.getSecondSubject();
        //工作餐明细内容
        ExpenseContentDto dto = request.getExpenseContentDto();
        String cityName = dto.getCityName();
        Integer travelDays = dto.getTravelDays();

        //境外工作餐科目、城市不为空、并且知道具体的国家 走差标配置获取预算金额
        if (ExpenseSecondSubjectEnum.JWGZC.getSubjectCode().equals(secondSubject) && StringUtils.isNotBlank(cityName) && cityName.contains("(") && !cityName.contains(TravelConstant.OVERSEAS)) {

            CTripCountryVO countryInfo = travelStandardService.selectCountryInfoByCountryName(dto.getCountryName());
            //城市code包含字母说明保存的时flight类型的地点code 需要转换
            String placeCode = StringUtil.match(StringUtil.azAzPattern, dto.getCity()) ? travelPlaceService.convertPlaceCode(TravelConstant.TYPE_HOTEL, dto.subOverseasCityName(), countryInfo.getCountryId()) : dto.getCity();

            return checkTravelMealBudgetStandard(placeCode, travelDays, countryInfo.getCountryId(), dto, request, ExpenseFirstSubjectEnum.CL_BUSINESS_MEAL.getSubjectCode());
        }

        //不知道具体国家的走下面的历史逻辑
        BigDecimal expenseAmount = request.getExpenseContentDto().getAmount();
        Map<String, Integer> expenseMealBudget = businessMealBudgetMap;
        Integer budget = expenseMealBudget.get(secondSubject);
        AssertHelper.notNull(budget, "未查询到工作餐预算配置，请联系OA薯");

        BigDecimal budgetAmount;
        if (ExpenseSecondSubjectEnum.JNGZC.getSubjectCode().equals(secondSubject)) {
            budgetAmount = BigDecimal.valueOf(budget);
        } else if (ExpenseSecondSubjectEnum.JWGZC.getSubjectCode().equals(secondSubject)) {
            BigDecimal exchangeRate = himalayaRpcService.getExchangeRate(DOLLAR_CODE);
            budgetAmount = BigDecimal.valueOf(budget).multiply(exchangeRate);
        } else {
            throw new BusinessException("未知工作餐预算类型：" + secondSubject);
        }

        BigDecimal budgetAmountRes = budgetAmount.multiply(BigDecimal.valueOf(travelDays));
        log.info("checkBusinessMealOverBudget request:{} budgetAmountRes:{},", request, budgetAmountRes);
        if (expenseAmount.compareTo(budgetAmountRes) > 0) {
            return true;
        }
        return false;
    }

    public SeatIsOverBudgetRequest buildSeatIsOverBudgetRequest(ExpenseDto dto) {
        String checkType = ExpenseFirstSubjectEnum.CL_TRAIN_TICKET.getSubjectCode().equals(dto.getFirstSubject())
                ? SEAT_CHECK_TYPE_TRAIN
                : SEAT_CHECK_TYPE_AIR;

        SeatIsOverBudgetRequest request = new SeatIsOverBudgetRequest();
        request.setFormType(dto.getFormType());
        request.setFirstSubject(dto.getFirstSubject());

        List<DynamicFormFieldDto> dynamicFormFieldDtoList = dto.getDynamicFormFieldDtoList();
        Object tripRouteCityObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_TRIP_ROUTE_CITY);
        if (dynamicFormFieldService.isValueEmpty(tripRouteCityObject)) {
            return null;
        }

        List<SeatContentDto> seatContentDtoList = JSONArray.parseArray(tripRouteCityObject.toString(), SeatContentDto.class);
        seatContentDtoList.stream()
                .filter(seat -> seat != null && StringUtils.isNotEmpty(seat.getSeatType()))
                .peek(seat -> seat.setCheckType(checkType))
                .collect(Collectors.toList());

        request.setCheckList(seatContentDtoList);
        return request;
    }


    @Override
    public ExpenseBudgetCheckRequest buildBusinessMealRequest(ExpenseDto dto) {
        if (ExpenseFirstSubjectEnum.CL_BUSINESS_MEAL.getSubjectCode().equals(dto.getFirstSubject())) {
            List<DynamicFormFieldDto> dynamicFormFieldDtoList = dto.getDynamicFormFieldDtoList();
            Object amountObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_AMOUNT);
            Object stayDaysObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_STAY_DAYS);
            Object cityObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_CITY_CODE);
            if (org.springframework.util.StringUtils.isEmpty(dto.getFormType())
                    || org.springframework.util.StringUtils.isEmpty(dto.getFirstSubject())
                    || org.springframework.util.StringUtils.isEmpty(dto.getSecondSubject())
                    || dynamicFormFieldService.isValueEmpty(amountObject)
                    || dynamicFormFieldService.isValueEmpty(stayDaysObject)
                    || (ExpenseSecondSubjectEnum.JWGZC.getSubjectCode().equals(dto.getSecondSubject()) && dynamicFormFieldService.isValueEmpty(cityObject))) {
                return null;
            } else {
                BigDecimal amount = BigDecimal.ZERO;
                if (amountObject instanceof String) {
                    amount = new BigDecimal((String) amountObject);
                } else if (amountObject instanceof Integer) {
                    amount = new BigDecimal((Integer) amountObject);
                }
                Integer stayDays = Integer.valueOf((String) stayDaysObject);

                // 校验是否超标
                ExpenseBudgetCheckRequest request = new ExpenseBudgetCheckRequest();
                request.setFormType(dto.getFormType());
                request.setFirstSubject(dto.getFirstSubject());
                request.setSecondSubject(dto.getSecondSubject());
                ExpenseContentDto expenseContentDto = new ExpenseContentDto();
                expenseContentDto.setAmount(amount);
                expenseContentDto.setTravelDays(stayDays);

                // 境内工作餐的城市校验不用传城市 直接用150做校验 境外工作餐需要传城市 每个城市标准不一样
                if (ExpenseSecondSubjectEnum.JWGZC.getSubjectCode().equals(dto.getSecondSubject())) {
                    TravelCityDto city = JSON.parseObject(cityObject.toString(), TravelCityDto.class);
                    expenseContentDto.setCity(city.getValue());
                    expenseContentDto.setCityName(city.getLabel());
                }
                request.setExpenseContentDto(expenseContentDto);
                return request;
            }
        }
        return null;
    }

    public ExpenseContentDto buildHotelRequest(ExpenseDto dto) {
        if (ExpenseFirstSubjectEnum.CL_HOTEL.getSubjectCode().equals(dto.getFirstSubject())) {
            List<DynamicFormFieldDto> dynamicFormFieldDtoList = dto.getDynamicFormFieldDtoList();
            Object amountObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_AMOUNT);
            Object stayDaysObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_STAY_DAYS);
            Object cityObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_CITY_CODE);

            if (StringUtils.isEmpty(dto.getFormType())
                    || StringUtils.isEmpty(dto.getFirstSubject())
                    || StringUtils.isEmpty(dto.getSecondSubject())
                    || dynamicFormFieldService.isValueEmpty(amountObject)
                    || dynamicFormFieldService.isValueEmpty(stayDaysObject)
                    || dynamicFormFieldService.isValueEmpty(cityObject)) {
                return null;
            } else {
                BigDecimal amount = new BigDecimal((String) amountObject);
                Integer stayDays = Integer.valueOf((String) stayDaysObject);
                TravelCityDto city = JSON.parseObject(cityObject.toString(), TravelCityDto.class);


                Object outsideRoommateObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_OUTSIDE_ROOMMATE);
                Object companyMembersObject = dynamicFormFieldService.getDefaultValueObject(dynamicFormFieldDtoList, CommonConstant.EXPENSE_FIELD_ID_CODE_COMPANY_MEMBERS);
                Boolean hasPartner = !dynamicFormFieldService.isValueEmpty(outsideRoommateObject) || !dynamicFormFieldService.isValueEmpty(companyMembersObject);

                // 校验是否超标
                ExpenseContentDto expenseContentDto = new ExpenseContentDto();
                expenseContentDto.setAmount(amount);
                expenseContentDto.setTravelDays(stayDays);
                expenseContentDto.setCity(city.getValue());
                expenseContentDto.setCityName(city.getLabel());
                expenseContentDto.setHasPartner(hasPartner);
                return expenseContentDto;
            }
        }
        return null;
    }


    public Boolean checkTravelMealBudgetStandard(String cityId, Integer travelDays, String countryId, ExpenseContentDto contentDto, ExpenseBudgetCheckRequest request, String subject) {

        TravelStandard travelStandard = getCommonTravelStandard(cityId, countryId, "", subject);

        BigDecimal totalBudget = travelStandard.getAmount().multiply(new BigDecimal(travelDays));
        BigDecimal exchangeRate = himalayaRpcService.getExchangeRate(travelStandard.getCurrency());

        BigDecimal totalBudgetAmount = totalBudget.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);
        boolean overBudget = contentDto.isOverBudget(totalBudgetAmount);
        if (overBudget) {
            request.getExpenseContentDto().getAmount().add(travelStandard.getAmount().multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP));
        }

        log.info("checkTravelMealBudgetStandard cityId:{},travelDays:{},countryId:{},contentDto{},request:{},subject:{}", cityId, travelDays, countryId, contentDto, request, subject);
        return overBudget;
    }
}
