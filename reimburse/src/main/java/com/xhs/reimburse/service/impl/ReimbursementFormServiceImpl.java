package com.xhs.reimburse.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.xhs.finance.sso.UserInfoBag;
import com.xhs.reimburse.assembler.ReimbursementFormAssembler;
import com.xhs.reimburse.enums.*;
import com.xhs.reimburse.mapper.ReimbursementFormMapper;
import com.xhs.reimburse.mapper.TReimbursementFormMapper;
import com.xhs.reimburse.modal.dto.*;
import com.xhs.reimburse.modal.entity.EmployeeEntity;
import com.xhs.reimburse.modal.entity.ReimbursementFormEntity;
import com.xhs.reimburse.modal.entity.ReimbursementFormExample;
import com.xhs.reimburse.modal.request.PageQueryReimburseFormRequest;
import com.xhs.reimburse.modal.request.ReimburseFormRequest;
import com.xhs.reimburse.modal.response.ReimbursementFormResponse;
import com.xhs.reimburse.modal.response.TravelFormResponse;
import com.xhs.reimburse.service.*;
import com.xhs.reimburse.service.external.ehr.EhrEmployeeRpcService;
import com.xiaohongshu.erp.common.exception.BusinessException;
import com.xiaohongshu.erp.common.framework.page.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.xhs.reimburse.constant.CommonConstant.NEED_AUDIT_AMOUNT;
import static com.xhs.reimburse.enums.ReimburseTypeEnum.CLBXD;

/**
 * <AUTHOR>
 * @date :2025/02/12 - 下午2:44
 * @description :
 */
@Slf4j
@Service
public class ReimbursementFormServiceImpl extends ServiceImpl<ReimbursementFormMapper, ReimbursementFormEntity> implements ReimbursementFormService {
    // service
    @Resource
    private InvoiceService invoiceService;

    @Resource
    private ExpenseService expenseService;

    @Resource
    private RelationExpenseInvoiceService relationExpenseInvoiceService;

    @Resource
    private RelationReimbursementFormExpenseService relationReimbursementFormExpenseService;

    @Resource
    private ReimburseCommonService reimburseCommonService;

    @Resource
    private ReimburseFormFlowService reimburseFormFlowService;

    @Resource
    private EhrEmployeeRpcService ehrEmployeeRpcService;

    // mapper
    @Resource
    private ReimbursementFormMapper reimbursementFormMapper;

    @Resource
    private TReimbursementFormMapper tReimbursementFormMapper;

    // convert
    @Resource
    private ReimbursementFormAssembler reimbursementFormAssembler;

    @Resource
    private TravelApplyExpenseFormRelationService travelApplyExpenseFormRelationService;

    @ApolloJsonValue("${bot_audit_gray_userId_list:[]}")
    private List<String> botAuditGrayUserIdList;

    // 是否关闭BOT机审
    @ApolloJsonValue("${close_bot_audit:false}")
    private boolean closeBotAudit;

    public List<ReimbursementFormEntity> queryReimbursementFormEntity(List<String> reimbursementFormUuidList) {
        if (CollectionUtils.isEmpty(reimbursementFormUuidList)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<ReimbursementFormEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ReimbursementFormEntity::getUuid, reimbursementFormUuidList);
        return reimbursementFormMapper.selectList(queryWrapper);
    }

    /**
     * 查询报销单
     *
     * @param reimbursementFormUuidList 报销单ID列表
     * @return 报销单列表
     */
    @Override
    public List<ReimbursementFormDto> queryReimbursementForm(List<String> reimbursementFormUuidList) {
        if(CollectionUtils.isEmpty(reimbursementFormUuidList)) {
            return new ArrayList<>();
        }
        List<ReimbursementFormEntity> entityList = queryReimbursementFormEntity(reimbursementFormUuidList);
        List<ReimbursementFormDto> reimbursementFormDtoList = reimbursementFormAssembler.toDtoList(entityList);
        reimbursementFormDtoList.forEach(reimbursementFormDto -> {
            String reimbursementFormUuid = reimbursementFormDto.getUuid();
            List<ExpenseDto> expenseDtoList = relationReimbursementFormExpenseService.getReimbursementFormAllExpense(reimbursementFormUuid);
            reimbursementFormDto.setRelationExpenseList(expenseDtoList);
            List<String> travelApplyFormNums = travelApplyExpenseFormRelationService.queryRelationTravelApplyNums(reimbursementFormUuid, true);
            reimbursementFormDto.setTravelApplyFormNums(travelApplyFormNums);
        });
        return reimbursementFormDtoList;
    }

    @Override
    public Integer getPendingCount() {
        List<Integer> pendingStatusList = ReimbursementFormStatusEnum.getCodeByQueryStatus("PENDING");
        return tReimbursementFormMapper.selectPendingCount(reimburseCommonService.getUserId(), pendingStatusList);
    }

    /**
     * 新增报销单
     * -- 新增不需要复杂校验，仅在保存时进行复杂校验
     *
     * @param dto 报销单实体
     * @return 新增好的报销单ID
     */
    @Override
    public String addReimbursementForm(ReimbursementFormDto dto) {
        ReimburseFormRequest request = new ReimburseFormRequest();
        request.setCreatorNo(dto.getCreatorNo());
        request.setFormType(dto.getFormType());
        request.setTravelApplyFormNums(dto.getTravelApplyFormNums());
        ReimbursementFormResponse reimbursementFormResponse = reimburseFormFlowService.saveOrSubmitReimbursementForm(request, true);
        return reimbursementFormResponse.getUuid();
    }

    /**
     * @param userId                    用户ID
     * @param reimbursementFormUuidList 报销单ID列表
     */
    @Override
    public void existCheck(String userId, List<String> reimbursementFormUuidList) {
        List<ReimbursementFormDto> expenseList = queryReimbursementForm(reimbursementFormUuidList);
        if (CollectionUtils.isEmpty(expenseList)) {
            throw new BusinessException("未找到该报销单");
        }

        List<String> userIdList = expenseList.stream().map(ReimbursementFormDto::getCreatorNo).distinct().collect(Collectors.toList());
        if (userIdList.size() != 1 || !userIdList.contains(userId)) {
            throw new BusinessException("非本人报销单");
        }
    }

    /**
     * 提交报销单，新增和更新表单内容
     */
    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "oaOfficeTransactionManager")
    public void addOrUpdateReimbursementForm4Submit(ReimbursementFormEntity reimburseForm) {
        if (Objects.isNull(reimburseForm.getId())) {
            //回写主键
            tReimbursementFormMapper.insertSelective(reimburseForm);
        } else {
            tReimbursementFormMapper.updateByUuidSelective(reimburseForm);
        }
    }

    @Override
    public PageResult<ReimbursementFormSimpleDto> pageQuery(PageQueryReimburseFormRequest request) {
        //分页查询自己的报销单
        request.setUserId(UserInfoBag.get().getUserId());

        Page<ReimbursementFormEntity> pageInfo = PageHelper.startPage(request.getPageNum(), request.getPageSize());
        tReimbursementFormMapper.pageQueryReimbursementForm(request);

        return reimbursementFormAssembler.toPageResult(pageInfo);
    }

    @Override
    public ReimbursementFormEntity getReimbursementFormByFormNum(String formNum, boolean withDeleted) {
        LambdaQueryWrapper<ReimbursementFormEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ReimbursementFormEntity::getFormNum, formNum);
        if (!withDeleted) {
            queryWrapper.eq(ReimbursementFormEntity::getIsValid, RecordStatusEnum.VALID.getCode());
        }
        ReimbursementFormEntity entity = reimbursementFormMapper.selectOne(queryWrapper);
        if (entity == null) {
            throw new BusinessException("单据不存在");
        }
        return entity;
    }

    @Override
    public void invalidReimbursementForm(String formNum) {
        if (StrUtil.isNotBlank(formNum)) {
            tReimbursementFormMapper.invalidReimbursementFormByFormNum(formNum);
        }
    }

    @Override
    public void invalidReimbursementFormByUuid(String uuid) {
        if (StrUtil.isNotBlank(uuid)) {
            tReimbursementFormMapper.invalidReimbursementFormByUuid(uuid);
        }
    }

    @Override
    public ReimbursementFormEntity findSelfReimburseForm(String formNum, String userId) {
        if (StrUtil.isBlank(formNum) || StrUtil.isBlank(userId)) {
            return null;
        }
        ReimbursementFormExample example = new ReimbursementFormExample();
        example.createCriteria().andIsValidEqualTo(1).andCreatorNoEqualTo(userId).andFormNumEqualTo(formNum);
        List<ReimbursementFormEntity> entities = tReimbursementFormMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(entities)) {
            return entities.get(0);
        }
        return null;
    }

    @Override
    public ReimbursementFormEntity findReimburseForm(String formNum) {
        if (StrUtil.isBlank(formNum)) {
            return null;
        }
        ReimbursementFormExample example = new ReimbursementFormExample();
        example.createCriteria().andIsValidEqualTo(1).andFormNumEqualTo(formNum);
        List<ReimbursementFormEntity> entities = tReimbursementFormMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(entities)) {
            return entities.get(0);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "oaOfficeTransactionManager")
    public void updateReimbursementFormAllStatus(String formNum, ReimbursementFormStatusEnum reimbursementFormStatus) {
        // 获得对应的状态包
        InvoiceFormStatusEnum invoiceFormStatus = getInvoiceFormStatus(reimbursementFormStatus);
        ExpenseFormStatusEnum expenseFormStatus = getExpenseFormStatus(reimbursementFormStatus);

        // 更新报销单状态
        ReimbursementFormEntity entity = getReimbursementFormByFormNum(formNum, false);
        entity.setReimburseStatus(reimbursementFormStatus.getCode());
        reimbursementFormMapper.updateById(entity);

        // 更新费用状态
        String reimbursementFormUuid = entity.getUuid();
        List<ExpenseDto> expenseDtoList = relationReimbursementFormExpenseService.getReimbursementFormAllExpense(reimbursementFormUuid);
        if (!CollectionUtils.isEmpty(expenseDtoList)) {
            List<String> expenseUuidList = expenseDtoList.stream().map(ExpenseDto::getUuid).collect(Collectors.toList());
            expenseService.updateExpenseListStatus(expenseUuidList, null, null, expenseFormStatus);
            // 更新发票状态
            List<String> invoiceUuidList = relationExpenseInvoiceService.getExpenseListAllInvoice(expenseUuidList);
            invoiceService.updateInvoiceListStatus(invoiceUuidList, null, null, invoiceFormStatus);
        }
    }

    private InvoiceFormStatusEnum getInvoiceFormStatus(ReimbursementFormStatusEnum reimbursementFormStatus) {
        // 草稿状态发票应该是已关联
        if (ReimbursementFormStatusEnum.CG.equals(reimbursementFormStatus)) {
            return InvoiceFormStatusEnum.YGL;
        }

        // 审核中：修改发票-报销单、费用-报销单状态
        if (ReimbursementFormStatusEnum.SHZ.equals(reimbursementFormStatus)) {
            return InvoiceFormStatusEnum.SHZ;
        }

        // 驳回：修改发票-报销单、费用-报销单状态
        if (ReimbursementFormStatusEnum.BH.equals(reimbursementFormStatus)) {
            return InvoiceFormStatusEnum.BH;
        }

        // 撤回：修改发票-报销单、费用-报销单状态
        if (ReimbursementFormStatusEnum.CH.equals(reimbursementFormStatus)) {
            return InvoiceFormStatusEnum.CH;
        }

        // 中止：修改发票-报销单、费用-报销单状态、
        if (ReimbursementFormStatusEnum.YZZ.equals(reimbursementFormStatus)) {
            return InvoiceFormStatusEnum.WGL;
        }

        // 删除：修改发票-报销单、费用-报销单状态、
        if (ReimbursementFormStatusEnum.YSC.equals(reimbursementFormStatus)) {
            return InvoiceFormStatusEnum.WGL;
        }

        // 单据完成：修改发票-报销单、费用-报销单状态、
        if (ReimbursementFormStatusEnum.YWJ.equals(reimbursementFormStatus)) {
            return InvoiceFormStatusEnum.YBX;
        }

        return null;
    }

    private ExpenseFormStatusEnum getExpenseFormStatus(ReimbursementFormStatusEnum reimbursementFormStatus) {
        // 草稿状态费用应该是已关联
        if (ReimbursementFormStatusEnum.CG.equals(reimbursementFormStatus)) {
            return ExpenseFormStatusEnum.YGL;
        }

        // 审核中：修改发票-报销单、费用-报销单状态
        if (ReimbursementFormStatusEnum.SHZ.equals(reimbursementFormStatus)) {
            return ExpenseFormStatusEnum.SHZ;
        }

        // 驳回：修改发票-报销单、费用-报销单状态
        if (ReimbursementFormStatusEnum.BH.equals(reimbursementFormStatus)) {
            return ExpenseFormStatusEnum.BH;
        }

        // 撤回：修改发票-报销单、费用-报销单状态
        if (ReimbursementFormStatusEnum.CH.equals(reimbursementFormStatus)) {
            return ExpenseFormStatusEnum.CH;
        }

        // 中止：修改发票-报销单、费用-报销单状态、
        if (ReimbursementFormStatusEnum.YZZ.equals(reimbursementFormStatus)) {
            return ExpenseFormStatusEnum.WGL;
        }

        // 删除：修改发票-报销单、费用-报销单状态、
        if (ReimbursementFormStatusEnum.YSC.equals(reimbursementFormStatus)) {
            return ExpenseFormStatusEnum.WGL;
        }

        // 单据完成：修改发票-报销单、费用-报销单状态、
        if (ReimbursementFormStatusEnum.YWJ.equals(reimbursementFormStatus)) {
            return ExpenseFormStatusEnum.YBX;
        }

        return null;
    }

    @Override
    public void updateReimbursementFormListStatus(List<String> reimbursementFormUuidList, ReimbursementFormStatusEnum reimbursementFormStatus) {
        List<ReimbursementFormEntity> reimbursementFormEntityList = queryReimbursementFormEntity(reimbursementFormUuidList);
        reimbursementFormEntityList.forEach(reimbursementFormEntity -> {
            reimbursementFormEntity.setReimburseStatus(reimbursementFormStatus.getCode());
            reimbursementFormMapper.updateById(reimbursementFormEntity);
        });
    }

    @Override
    public String getPaymentCompanyName( ReimbursementFormEntity reimbursementFormEntity) {
        if (reimbursementFormEntity == null || StringUtils.isEmpty(reimbursementFormEntity.getFormContent())) {
            return "";
        }

        JSONObject data = JSONObject.parse(reimbursementFormEntity.getFormContent());
        JSONObject basicInfoVoObject = data.getJSONObject("reimburseBasicInfoVo");
        if(basicInfoVoObject == null) {
            return "";
        }
        ReimburseBasicInfo reimburseBasicInfo = basicInfoVoObject.toJavaObject(ReimburseBasicInfo.class);
        return reimburseBasicInfo.getPaymentCompanyName();
    }

    /**
     * 修改报销单form_content字段中冗余的费用列表字段
     *
     * @param reimbursementFormUuid
     * @param expenseUuidList
     */
    @Override
    public void updateReimbursementFormRelationFild(String reimbursementFormUuid, List<String> expenseUuidList) {
        List<ReimbursementFormEntity> reimbursementFormEntityList = queryReimbursementFormEntity(Lists.newArrayList(reimbursementFormUuid));
        ReimbursementFormEntity reimbursementFormEntity = reimbursementFormEntityList.get(0);
        JSONObject formContent = JSONObject.parseObject(reimbursementFormEntity.getFormContent());
        formContent.put("expenseNos", expenseUuidList);
        reimbursementFormEntity.setFormContent(formContent.toJSONString());
        reimbursementFormMapper.updateById(reimbursementFormEntity);
    }

    private List<String> getTravalApplyFormNumList(ReimbursementFormEntity entity) {
        return Optional.ofNullable(entity)
                .map(ReimbursementFormEntity::getFormContent)
                .map(JSONObject::parseObject)
                .map(formContent -> formContent.getJSONArray("travelApplyFormNums"))
                .map(travelApplyFormNums -> travelApplyFormNums.toJavaList(String.class))
                .orElse(new ArrayList<>());
    }

    @Override
    public ReimbursementFormCheckResultDto checkReimbursementForm(String formNum, String userId) {
        ReimbursementFormCheckResultDto result = new ReimbursementFormCheckResultDto();
        // 查询报销单详情
        ReimbursementFormResponse response = reimburseFormFlowService.queryReimburseFormDetail(formNum);

        // 报销单的关联费用列表中才有阻碍项信息
        List<ExpenseDto> expenseList = Optional.ofNullable(response.getExpenses()).orElse(new ArrayList<>());

        // 费用的关联发票列表中才有阻碍项信息
        List<InvoiceDto> invoiceList = expenseList.stream()
                .map(ExpenseDto::getUuid)
                .map(expenseUuid -> expenseService.getExpenseDtoByUuid(expenseUuid, false))
                .filter(Objects::nonNull)
                .map(ExpenseDto::getRelationInvoiceList)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        // 报销单维度的校验
        checkReimbursementFormInForm(result, response, expenseList, invoiceList, userId);

        // 费用维度的校验
        expenseService.checkReimbursementFormInExpense(result, expenseList);

        // 发票维度的校验
        invoiceService.checkReimbursementFormInInvoice(result, invoiceList);

        // 如果校验项阻碍提交，则校验失败
        boolean blockSubmit = result.getCheckResultDetailList().stream().anyMatch(CheckResultDetailDto::getBlockSubmit);

        // 是否需要审核
        boolean needAudit = result.getCheckResultDetailList().stream().anyMatch(CheckResultDetailDto::getNeedAudit);

        result.setCheckPass(!blockSubmit);
        result.setNeedAudit(needAudit);
        return result;
    }

    /**
     * 提交报销单时，报销单维度的校验
     * @param result 校验结果
     * @param response 报销单实体
     * @param userId 用户ID
     */
    private void checkReimbursementFormInForm(ReimbursementFormCheckResultDto result, ReimbursementFormResponse response, List<ExpenseDto> expenseList, List<InvoiceDto> invoiceList, String userId) {
        List<ReimbursementFormCheckItemEnum> checkItemList = new ArrayList<>();

        // 用户身份校验
        formAuthCheck(checkItemList, response, userId);

        // 单据状态校验
        formStatusCheck(checkItemList, response);

        // 单据基础信息校验
        formBaseInfoCheck(result, response);

        // 单据关联费用校验
        formRelateExpenseCheck(checkItemList, response);

        // 单据单独提交校验
        formSingleSubmitCheck(checkItemList, response);

        // 单据行程关联校验
        formRelateTravelApplyCheck(checkItemList, response);

        // 单据发票多主体校验
        formInvoiceMultiCompanyCheck(checkItemList, response, invoiceList);

        // 单据报销金额校验
        formAmountCheck(checkItemList, response);

        // 单据提报人特征校验
        formCreatorFeatureCheck(checkItemList, response);

        // 单据扫码交单校验
        formScanSubmissionCheck(checkItemList, response, expenseList);

        // 统一处理checkItemList
        checkItemList.stream()
                .map(checkItem -> CheckResultDetailDto.builder()
                        .checkItemCode(checkItem.getCheckItemCode())
                        .checkItemName(checkItem.getCheckItemName())
                        .blockSubmit(checkItem.getBlockSubmit())
                        .build())
                .forEach(result.getCheckResultDetailList()::add);
    }

    /**
     * 单据扫码交单校验
     * @param checkItemList 未通过的校验项列表
     * @param response 报销单实体
     */
    private void formScanSubmissionCheck(List<ReimbursementFormCheckItemEnum> checkItemList, ReimbursementFormResponse response, List<ExpenseDto> expenseList) {
        ReimbursementFormCheckItemEnum checkItem = ReimbursementFormCheckItemEnum.FORM_SCAN_SUBMISSION_CHECK;
        if(relationExpenseInvoiceService.confirmNeedSubmitInvoice(expenseList)) {
            checkItemList.add(checkItem);
        }
    }

    /**
     * 单据提报人特征校验
     * @param checkItemList 未通过的校验项列表
     * @param response 报销单实体
     */
    private void formCreatorFeatureCheck(List<ReimbursementFormCheckItemEnum> checkItemList, ReimbursementFormResponse response) {
        ReimbursementFormCheckItemEnum checkItem = ReimbursementFormCheckItemEnum.FORM_CREATOR_FEATURE_CHECK;
        String userId = response.getCreatorNo();
        if (StringUtils.isEmpty(userId)) {
            return;
        }

        EmployeeEntity employeeEntity = ehrEmployeeRpcService.queryEhrEmployeeEntity(userId, true);
        if (Objects.isNull(employeeEntity)) {
            return;
        }

        // 提单人非正式员工走人审
        if (!Objects.equals(employeeEntity.getEmployeeType(), EmployeeTypeEnum.FORMAL_EMP.getCode())) {
            checkItemList.add(checkItem);
        }
    }

    /**
     * 单据报销金额校验
     * @param checkItemList 未通过的校验项列表
     * @param response 报销单实体
     */
    private void formAmountCheck(List<ReimbursementFormCheckItemEnum> checkItemList, ReimbursementFormResponse response) {
        ReimbursementFormCheckItemEnum checkItem = ReimbursementFormCheckItemEnum.FORM_AMOUNT_CHECK;
        BigDecimal amount = response.getAmount();
        if (Objects.isNull(amount)) {
            return;
        }

        // 报销金额超2k的报销单走人审
        if (amount.compareTo(NEED_AUDIT_AMOUNT) > 0) {
            checkItemList.add(checkItem);
        }
    }

    /**
     * 单据发票多主体校验
     * @param checkItemList 未通过的校验项列表
     * @param response 报销单实体
     */
    private void formInvoiceMultiCompanyCheck(List<ReimbursementFormCheckItemEnum> checkItemList, ReimbursementFormResponse response, List<InvoiceDto> invoiceList) {
        ReimbursementFormCheckItemEnum checkItem = ReimbursementFormCheckItemEnum.FORM_INVOICE_MULTI_COMPANY_CHECK;
        if (CollectionUtils.isEmpty(invoiceList)) {
            return;
        }

        // 报销单下的发票有多个主体校验
        List<String> purchaserTaxNoList = invoiceList.stream().map(InvoiceDto::getPurchaserTaxNo).collect(Collectors.toList());
        if (purchaserTaxNoList.size() > 1) {
            checkItemList.add(checkItem);
        }
    }

    /**
     * 单据行程关联校验
     * @param checkItemList 未通过的校验项列表
     * @param response 报销单实体
     */
    private void formRelateTravelApplyCheck(List<ReimbursementFormCheckItemEnum> checkItemList, ReimbursementFormResponse response) {
        ReimbursementFormCheckItemEnum checkItem = ReimbursementFormCheckItemEnum.FORM_RELATE_TRAVEL_APPLY_CHECK;

        // 校验未通过
        if (StringUtils.equals(CLBXD.getType(), response.getFormType())) {
            TravelFormResponse travelFormResponse = (TravelFormResponse) response;
            List<String> travelApplyFormNums = travelFormResponse.getTravelApplyFormNums();
            if (CollectionUtils.isEmpty(travelApplyFormNums)) {
                checkItemList.add(checkItem);
            }
        }
    }

    /**
     * 单据单独提交校验
     * @param checkItemList 未通过的校验项列表
     * @param response 报销单实体
     */
    private void formSingleSubmitCheck(List<ReimbursementFormCheckItemEnum> checkItemList, ReimbursementFormResponse response) {
        ReimbursementFormCheckItemEnum checkItem = ReimbursementFormCheckItemEnum.FORM_SINGLE_SUBMIT_CHECK;
        List<ExpenseDto> expenseList = response.getExpenses();
        String checkResult = expenseService.formSingleSubmitCheck(expenseList);

        // 校验未通过
        if (StringUtils.isNotBlank(checkResult)) {
            checkItemList.add(checkItem);
        }
    }

    /**
     * 单据关联费用校验
     * @param checkItemList 未通过的校验项列表
     * @param response 报销单实体
     */
    private void formRelateExpenseCheck(List<ReimbursementFormCheckItemEnum> checkItemList, ReimbursementFormResponse response) {
        ReimbursementFormCheckItemEnum checkItem = ReimbursementFormCheckItemEnum.FORM_RELATE_EXPENSE_CHECK;
        List<ExpenseDto> expenseList = response.getExpenses();
        String checkResult = expenseService.formRelateExpenseCheck(expenseList);

        // 校验未通过
        if (StringUtils.isNotBlank(checkResult)) {
            checkItemList.add(checkItem);
        }
    }

    /**
     * 用户身份校验
     * @param checkItemList 未通过的校验项列表
     * @param userId 用户ID
     * @param response 报销单实体
     */
    private void formAuthCheck(List<ReimbursementFormCheckItemEnum> checkItemList, ReimbursementFormResponse response, String userId) {
        ReimbursementFormCheckItemEnum checkItem = ReimbursementFormCheckItemEnum.FORM_AUTH_CHECK;
        String creatorNo = response.getCreatorNo();

        // 校验未通过
        if (StringUtils.isEmpty(userId) || !StringUtils.equals(userId, creatorNo)) {
            checkItemList.add(checkItem);
        }
    }

    /**
     * 单据状态校验
     * @param checkItemList 未通过的校验项列表
     * @param response 报销单实体
     */
    private void formStatusCheck(List<ReimbursementFormCheckItemEnum> checkItemList, ReimbursementFormResponse response) {
        ReimbursementFormCheckItemEnum checkItem = ReimbursementFormCheckItemEnum.FORM_STATUS_CHECK;
        Integer reimburseStatus = response.getReimburseStatus();

        // 校验未通过
        if (!ReimbursementFormStatusEnum.canEditStatus(reimburseStatus)) {
            checkItemList.add(checkItem);
        }
    }

    /**
     * 单据基础信息校验
     * @param result 校验结果
     * @param response 报销单实体
     */
    private void formBaseInfoCheck(ReimbursementFormCheckResultDto result, ReimbursementFormResponse response) {
        List<ReimbursementFormBaseInfoEnum> fieldList = new ArrayList<>();
        List<BlockItemDto> blockItemList = new ArrayList<>();
        ReimbursementFormCheckItemEnum checkItem = ReimbursementFormCheckItemEnum.FORM_BASE_INFO_CHECK;
        ReimburseBasicInfo reimburseBasicInfo = Optional.ofNullable(response.getReimburseBasicInfoVo()).orElse(new ReimburseBasicInfo());

        // 校验所有阻碍项
        if (StringUtils.isBlank(reimburseBasicInfo.getPaymentCompanyName())) {
            fieldList.add(ReimbursementFormBaseInfoEnum.PAYMENT_COMPANY_NAME);
        }
        if (StringUtils.isBlank(reimburseBasicInfo.getGatheringBank())) {
            fieldList.add(ReimbursementFormBaseInfoEnum.GATHERING_BANK);
        }
        if (StringUtils.isBlank(reimburseBasicInfo.getGatheringBankCode())) {
            fieldList.add(ReimbursementFormBaseInfoEnum.GATHERING_BANK_CODE);
        }
        if (StringUtils.isBlank(reimburseBasicInfo.getGatheringAccount())) {
            fieldList.add(ReimbursementFormBaseInfoEnum.GATHERING_ACCOUNT);
        }
        if (StringUtils.isBlank(reimburseBasicInfo.getGatheringName())) {
            fieldList.add(ReimbursementFormBaseInfoEnum.GATHERING_NAME);
        }
        if (StringUtils.isBlank(reimburseBasicInfo.getDepartmentId())) {
            fieldList.add(ReimbursementFormBaseInfoEnum.DEPARTMENT_ID);
        }
        if (CollectionUtils.isEmpty(reimburseBasicInfo.getDepartmentIdPath())) {
            fieldList.add(ReimbursementFormBaseInfoEnum.DEPARTMENT_ID_PATH);
        }
        if (StringUtils.isBlank(reimburseBasicInfo.getBudgetDepartmentId())) {
            fieldList.add(ReimbursementFormBaseInfoEnum.BUDGET_DEPARTMENT_ID);
        }
        if (CollectionUtils.isEmpty(reimburseBasicInfo.getBudgetDepartmentNamePath())) {
            fieldList.add(ReimbursementFormBaseInfoEnum.BUDGET_DEPARTMENT_NAME_PATH);
        }

        // 为发现的阻碍项构造 BlockItemDto
        fieldList.stream()
                .map(field -> BlockItemDto.builder()
                        .fieldCode(field.getFieldCode())
                        .fieldName(field.getFieldName())
                        .build())
                .forEach(blockItemList::add);

        // 有阻碍项
        if (CollectionUtils.isNotEmpty(blockItemList)) {
            CheckResultDetailDto detail = CheckResultDetailDto.builder()
                    .checkItemCode(checkItem.getCheckItemCode())
                    .checkItemName(checkItem.getCheckItemName())
                    .blockSubmit(checkItem.getBlockSubmit())
                    .blockItemList(blockItemList)
                    .build();
            result.getCheckResultDetailList().add(detail);
        }
    }

    public boolean formNeedAudit(String formNum) {
        ReimbursementFormEntity reimbursementForm = getReimbursementFormByFormNum(formNum, false);
        String userId = reimbursementForm.getCreatorNo();
        // 关闭BOT机审，则所有单据必须要审核
        if (closeBotAudit) {
            return true;
        }

        // 开启BOT机审，且用户不再灰度名单，则必须要审核
        if (!botAuditGrayUserIdList.contains(userId)) {
            return true;
        }

        ReimbursementFormCheckResultDto checkResult = checkReimbursementForm(formNum, userId);
        log.info("formNum = {}, checkResult = {}", formNum, JSONObject.toJSONString(checkResult));
        Boolean checkPass = checkResult.getCheckPass();
        // 校验不通过，则必须要审核
        if (!checkPass) {
            return true;
        }
        return checkResult.getNeedAudit();
    }
}