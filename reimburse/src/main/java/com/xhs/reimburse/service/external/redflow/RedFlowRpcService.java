package com.xhs.reimburse.service.external.redflow;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.xhs.finance.utils.AssertUtils;
import com.xhs.oa.office.exception.BusinessException;
import com.xhs.oa.office.utils.AssertHelper;
import com.xhs.reimburse.modal.request.RedFlowProcessSaveOrStartRequest;
import com.xiaohongshu.fls.finance.rpc.workflow.process.operator.OaFlowRuntimeProcessRpc;
import com.xiaohongshu.fls.finance.rpc.workflow.task.operator.OaFlowRuntimeTaskRpc;
import com.xiaohongshu.fls.rpc.finance.workflow.process.req.*;
import com.xiaohongshu.fls.rpc.finance.workflow.process.resp.OaCurrentTaskInfo;
import com.xiaohongshu.fls.rpc.finance.workflow.process.resp.OaRpcProcessInfoResp;
import com.xiaohongshu.fls.rpc.finance.workflow.process.response.OaRpcCheckUserFormPremissionResp;
import com.xiaohongshu.fls.rpc.finance.workflow.process.response.OaRpcProcessCurrentInfoResp;
import com.xiaohongshu.fls.rpc.finance.workflow.process.response.OaRpcSaveProcessResp;
import com.xiaohongshu.fls.rpc.finance.workflow.task.req.OaRpcFallBackReq;
import com.xiaohongshu.fls.rpc.finance.workflow.task.req.OaRpcTaskCompleteReq;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedFlowRpcService {

    @Resource
    private OaFlowRuntimeTaskRpc.Iface oaFlowRuntimeTaskRpc;

    @Resource
    private OaFlowRuntimeProcessRpc.Iface oaFlowRuntimeProcessRpcService;

    /**
     * 保存redFlow单据
     *
     * @param request 保存请求
     * @return 返回单据号，返回接口不为空
     */
    public String saveProcess(RedFlowProcessSaveOrStartRequest request) {
        OaRpcSaveProcessResp resp = null;
        try {
            OaRpcSaveProcessReq req = convertToSaveProcessReq(request);
            log.info("RedFlowRpcService saveProcess req: {}", JSON.toJSONString(req));
            resp = oaFlowRuntimeProcessRpcService.saveProcess(new Context(), req);
        } catch (TException e) {
            log.error("RedFlowRpcService saveProcess error:{}", e.getMessage(), e);
        }
        AssertUtils.notNull(resp, "报销单保存失败:流程保存失败，请联系OA薯");

        AssertUtils.check(resp.getResponse().success, resp.getResponse().msg);

        AssertUtils.notBlank(resp.getFormNum(), "报销单保存失败");

        return resp.getFormNum();
    }

    /**
     * 提交redFlow单据
     *
     * @param request 提交请求
     * @return 返回单据号，返回接口不为空
     */
    public String submitProcess(RedFlowProcessSaveOrStartRequest request) {
        //判断是否再提交，是走再提交接口
        if (isRestartProcess(request.getFormNum())) {
            return restartProcess(request);
        }

        OaRpcProcessCurrentInfoResp resp = null;
        try {

            OaRpcStartProcessReq req = convertToStartProcessReq(request);
            log.info("RedFlowRpcService submitProcess req: {}", JSON.toJSONString(req));
            resp = oaFlowRuntimeProcessRpcService.startProcess(new Context(), req);
        } catch (TException e) {
            log.error("RedFlowRpcService submitProcess error:{}", e.getMessage(), e);
        }
        AssertUtils.notNull(resp, "报销单提交失败:流程启动失败，请联系OA薯");

        AssertUtils.check(resp.getResponse().success, resp.getResponse().msg);

        OaRpcProcessInfoResp processInfo = resp.getProcessCurrentRpcResp();

        return processInfo.getFormId();
    }

    /**
     * 再次提交redFlow单据
     *
     * @param request 提交请求
     * @return 返回单据号，返回接口不为空
     */
    public String restartProcess(RedFlowProcessSaveOrStartRequest request) {
        com.xiaohongshu.fls.rpc.finance.workflow.task.response.OaRpcProcessCurrentInfoResp resp = null;
        try {
            OaRpcTaskCompleteReq req = convertToRestartProcessReq(request);
            log.info("RedFlowRpcService restartProcess req: {}", JSON.toJSONString(req));
            resp = oaFlowRuntimeTaskRpc.restartProcess(new Context(), req);
        } catch (TException e) {
            log.error("RedFlowRpcService restartProcess error:{}", e.getMessage(), e);
        }
        AssertUtils.notNull(resp, "报销单提交失败:流程启动失败，请联系OA薯");

        AssertUtils.check(resp.getResponse().success, resp.getResponse().msg);

        OaRpcProcessInfoResp processInfo = resp.getProcessCurrentRpcResp();

        return processInfo.getFormId();
    }

    /**
     * 驳回到任意节点
     *
     * @param request 驳回到任意节点请求
     * @return 流程当前信息响应
     */
    public com.xiaohongshu.fls.rpc.finance.workflow.task.response.OaRpcProcessCurrentInfoResp fallBackNode(OaRpcFallBackReq request) {
        com.xiaohongshu.fls.rpc.finance.workflow.task.response.OaRpcProcessCurrentInfoResp resp = null;
        try {
            log.info("RedFlowRpcService fallBackNode req: {}", JSON.toJSONString(request));
            resp = oaFlowRuntimeTaskRpc.fallBackNode(new Context(), request);
        } catch (TException e) {
            log.error("RedFlowRpcService fallBackNode error:{}", e.getMessage(), e);
        }
        if (resp == null || resp.getResponse() == null || !resp.getResponse().success) {
            log.info("RedFlowRpcService fallBackNode error: {}", JSON.toJSONString(resp));
        }
        return resp;
    }

    /**
     * 完成审批任务
     *
     * @param userId  用户ID不能为空
     * @param formNum 单据号
     * @return 流程当前信息响应
     */
    public com.xiaohongshu.fls.rpc.finance.workflow.task.response.OaRpcProcessCurrentInfoResp completeTask(String userId, String formNum) {
        com.xiaohongshu.fls.rpc.finance.workflow.task.response.OaRpcProcessCurrentInfoResp resp = null;
        try {
            OaRpcTaskCompleteReq req = new OaRpcTaskCompleteReq();
            req.setUserId(userId);
            req.setFormNum(formNum);
            log.info("RedFlowRpcService completeTask req: {}", JSON.toJSONString(req));
            resp = oaFlowRuntimeTaskRpc.completeTask(new Context(), req);
        } catch (TException e) {
            log.error("RedFlowRpcService completeTask error:{}", e.getMessage(), e);
        }
        if (resp == null || resp.getResponse() == null || !resp.getResponse().success) {
            log.error("RedFlowRpcService completeTask failed: {}", JSON.toJSONString(resp));
            throw new BusinessException("审批任务完成失败: " + (resp != null && resp.getResponse() != null ? resp.getResponse().msg : "系统异常"));
        }
        return resp;
    }

    /**
     * 根据当前用户访问单据鉴权
     *
     * @param currentUserId 当前用户编号
     * @param formNum       访问单据
     * @return 是否有权限
     */
    public boolean checkUserFormPermission(String currentUserId, String formNum) {
        OaRpcCheckUserFormPremissionResp resp = null;
        try {
            CheckUserFormPremissionReq req = new CheckUserFormPremissionReq(formNum, currentUserId);
            log.info("RedFlowRpcService checkUserFormPermission req: {}", JSON.toJSONString(req));
            resp = oaFlowRuntimeProcessRpcService.checkUserFormPremission(new Context(), req);
        } catch (TException e) {
            log.error("RedFlowRpcService submitProcess error:{}", e.getMessage(), e);
        }
        AssertUtils.notNull(resp, "流程鉴权失败: 系统异常");

        return Objects.nonNull(resp) && resp.isHasPremission();
    }

    /**
     * 删除redFlow单据
     *
     * @param formType 单据类型
     * @param uuid     业务ID
     */
    public void deleteForm(String formType, String uuid) {
        OaDeleteProcessByBusinessReq req = new OaDeleteProcessByBusinessReq();
        try {
            req.setBusinessId(uuid).setFormType(formType);
            OaRpcProcessCurrentInfoResp resp = oaFlowRuntimeProcessRpcService.deleteProcessByBusiness(new Context(), req);
            AssertHelper.check(resp.getResponse().success, resp.getResponse().msg);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("RedFlowRpcService deleteForm error:{}", e.getMessage(), e);
            throw new BusinessException("删除单据失败");
        }
    }

    /**
     * 是否是重新提交流程
     *
     * @param formNum 单据号
     * @return 重新提交流程 true:走restart接口、false:走start接口
     */
    private boolean isRestartProcess(String formNum) {
        if (StrUtil.isBlank(formNum)) {
            return false;
        }
        OaRpcProcessCurrentInfoResp processInfo = null;
        try {
            log.info("RedFlowRpcService isRestartProcess formNum:{}", formNum);
            processInfo = oaFlowRuntimeProcessRpcService.getProcessInfoByFormNum(new Context(), new OaRpcProcessInfoByFormReq(formNum));
            log.info("RedFlowRpcService isRestartProcess resp:{}", JSON.toJSONString(processInfo));
            AssertHelper.notNull(processInfo, "流程信息获取失败");
            AssertHelper.check(processInfo.getResponse().success, processInfo.getResponse().msg);
            OaRpcProcessInfoResp currentRpcResp = processInfo.getProcessCurrentRpcResp();
            if (Objects.isNull(currentRpcResp) || "CREATE".equals(currentRpcResp.getProcessStatus())) {
                return false;
            }
            OaCurrentTaskInfo currentTaskInfo = currentRpcResp.getCurrentTaskList().get(0);
            return "发起人提交".equals(currentTaskInfo.getTaskNodeName());
        } catch (TException e) {
            log.error("RedFlowRpcService isRestartProcess error:{}", e.getMessage(), e);
        }
        return false;
    }

    private OaRpcStartProcessReq convertToStartProcessReq(RedFlowProcessSaveOrStartRequest request) {
        OaRpcStartProcessReq startProcessReq = new OaRpcStartProcessReq();
        startProcessReq.setFormType(request.getFormType());
        startProcessReq.setFormNum(request.getFormNum());
        startProcessReq.setBusinessId(request.getUuid());
        startProcessReq.setJsonData(JSON.toJSONString(request.getVariableMap()));
        startProcessReq.setUserId(request.getStartUserId());
        return startProcessReq;
    }

    private OaRpcSaveProcessReq convertToSaveProcessReq(RedFlowProcessSaveOrStartRequest request) {
        OaRpcSaveProcessReq saveProcessReq = new OaRpcSaveProcessReq();
        saveProcessReq.setFormType(request.getFormType());
        saveProcessReq.setFormNum(request.getFormNum());
        saveProcessReq.setBusinessId(request.getUuid());
        saveProcessReq.setJsonData(JSON.toJSONString(request.getVariableMap()));
        saveProcessReq.setUserId(request.getStartUserId());
        return saveProcessReq;
    }

    private OaRpcTaskCompleteReq convertToRestartProcessReq(RedFlowProcessSaveOrStartRequest request) {
        OaRpcTaskCompleteReq req = new OaRpcTaskCompleteReq();
        req.setUserId(request.getStartUserId());
        req.setFormData(JSONObject.toJSONString(request.getVariableMap()));
        req.setFormNum(request.getFormNum());
        return req;
    }
}
