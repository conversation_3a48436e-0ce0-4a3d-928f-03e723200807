package com.xhs.reimburse.xhsoa.modal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * form_payment_detail 付款详情表实体
 */
@Data
@TableName("form_payment_detail")
public class FormPaymentDetail implements Serializable {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 付款编号
     */
    @TableField("payment_no")
    private String paymentNo;

    /**
     * 单据编号
     */
    @TableField("form_no")
    private String formNo;

    /**
     * 单据收款明细编号
     */
    @TableField("gathering_detail_no")
    private String gatheringDetailNo;

    /**
     * 支付状态
     */
    @TableField("payment_status")
    private String paymentStatus;

    /**
     * 付款金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 收款人
     */
    @TableField("gathering_name")
    private String gatheringName;

    /**
     * 付款公司
     */
    @TableField("subject")
    private String subject;

    /**
     * 0:对公 1:对私
     */
    @TableField("priate_or_public")
    private Integer priateOrPublic;

    /**
     * 收款账号
     */
    @TableField("gathering_account")
    private String gatheringAccount;

    /**
     * 银行名称
     */
    @TableField("bank_name")
    private String bankName;

    /**
     * 银行号
     */
    @TableField("bank_code")
    private String bankCode;

    /**
     * 币种
     */
    @TableField("payment_currency")
    private String paymentCurrency;

    /**
     * 银行类别码 IBAN ,SWIFT (境外) 境外必填
     */
    @TableField("hw_bank_code_type")
    private String hwBankCodeType;

    /**
     * swift code 境外需要
     */
    @TableField("hw_bank_code")
    private String hwBankCode;

    /**
     * 联系电话，如果付款的是韩币 则必要
     */
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 付款完成时间
     */
    @TableField("payment_success_time")
    private Date paymentSuccessTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建人id
     */
    @TableField("creator_no")
    private String creatorNo;

    /**
     * 创建人名称
     */
    @TableField("creator")
    private String creator;

    /**
     * 更新人id
     */
    @TableField("updator_no")
    private String updatorNo;

    /**
     * 更新人名称
     */
    @TableField("updator")
    private String updator;

    /**
     * 是否是境外
     */
    @TableField("is_oversea")
    private Integer isOversea;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 收款账号类型FU、SO、TI、TO、
     */
    @TableField("sub_beneficiary_account_type")
    private String subBeneficiaryAccountType;

    /**
     * 0:非跨境,1跨境
     */
    @TableField("cross_border")
    private Integer crossBorder;

    /**
     * 支付备注
     */
    @TableField("payment_detail_remark")
    private String paymentDetailRemark;

    /**
     * 是否有效：1-有效，0-无效
     */
    @TableField("is_valid")
    private Integer isValid;
}
