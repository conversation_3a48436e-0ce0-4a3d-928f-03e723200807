package com.xhs.reimburse.xhsoa.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhs.reimburse.xhsoa.modal.FormPaymentDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * form_payment_detail 付款详情表 Mapper
 */
@Mapper
public interface FormPaymentDetailMapper extends BaseMapper<FormPaymentDetail> {

    /**
     * 根据单据号查询付款详情记录数
     *
     * @param formNo 单据号
     * @return 记录数
     */
    default int countByFormNo(@Param("formNo") String formNo) {
        return selectCount(new QueryWrapper<FormPaymentDetail>()
                .eq("form_no", formNo)
                .eq("is_valid", 1));
    }

    /**
     * 插入付款详情记录
     *
     * @param formPaymentDetail 付款详情
     * @return 影响行数
     */
    default int insertPaymentDetail(FormPaymentDetail formPaymentDetail) {
        formPaymentDetail.setCreateTime(new Date());
        formPaymentDetail.setUpdateTime(new Date());
        formPaymentDetail.setIsValid(1);
        return insert(formPaymentDetail);
    }

    /**
     * 根据付款编号查询付款详情
     *
     * @param paymentNo 付款编号
     * @return 付款详情
     */
    default FormPaymentDetail selectByPaymentNo(@Param("paymentNo") String paymentNo) {
        return selectOne(new QueryWrapper<FormPaymentDetail>()
                .eq("payment_no", paymentNo)
                .eq("is_valid", 1));
    }

    /**
     * 根据单据号和付款编号查询付款详情
     *
     * @param formNo    单据号
     * @param paymentNo 付款编号
     * @return 付款详情
     */
    default FormPaymentDetail selectByFormNoAndPaymentNo(@Param("formNo") String formNo, 
                                                        @Param("paymentNo") String paymentNo) {
        return selectOne(new QueryWrapper<FormPaymentDetail>()
                .eq("form_no", formNo)
                .eq("payment_no", paymentNo)
                .eq("is_valid", 1));
    }

    /**
     * 更新付款状态
     *
     * @param paymentNo     付款编号
     * @param paymentStatus 支付状态
     * @param reason        失败原因
     * @return 影响行数
     */
    default int updatePaymentStatus(@Param("paymentNo") String paymentNo,
                                    @Param("paymentStatus") String paymentStatus,
                                    @Param("reason") String reason) {
        FormPaymentDetail updateEntity = new FormPaymentDetail();
        updateEntity.setPaymentStatus(paymentStatus);
        updateEntity.setReason(reason);
        updateEntity.setUpdateTime(new Date());

        return update(updateEntity, new UpdateWrapper<FormPaymentDetail>()
                .eq("payment_no", paymentNo)
                .eq("is_valid", 1));
    }
}
